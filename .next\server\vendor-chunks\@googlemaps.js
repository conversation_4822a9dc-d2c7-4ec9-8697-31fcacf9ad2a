"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@googlemaps";
exports.ids = ["vendor-chunks/@googlemaps"];
exports.modules = {

/***/ "(ssr)/./node_modules/@googlemaps/js-api-loader/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@googlemaps/js-api-loader/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_ID: () => (/* binding */ DEFAULT_ID),\n/* harmony export */   Loader: () => (/* binding */ Loader),\n/* harmony export */   LoaderStatus: () => (/* binding */ LoaderStatus)\n/* harmony export */ });\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\n\r\nfunction __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\nfunction getDefaultExportFromCjs (x) {\n\treturn x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;\n}\n\n// do not edit .js files directly - edit src/index.jst\n\n\n\nvar fastDeepEqual = function equal(a, b) {\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n\n\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n\n    for (i = length; i-- !== 0;)\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n\n    for (i = length; i-- !== 0;) {\n      var key = keys[i];\n\n      if (!equal(a[key], b[key])) return false;\n    }\n\n    return true;\n  }\n\n  // true if both NaN, false otherwise\n  return a!==a && b!==b;\n};\n\nvar isEqual = /*@__PURE__*/getDefaultExportFromCjs(fastDeepEqual);\n\n/**\n * Copyright 2019 Google LLC. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at.\n *\n *      Http://www.apache.org/licenses/LICENSE-2.0.\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst DEFAULT_ID = \"__googleMapsScriptId\";\n/**\n * The status of the [[Loader]].\n */\nvar LoaderStatus;\n(function (LoaderStatus) {\n    LoaderStatus[LoaderStatus[\"INITIALIZED\"] = 0] = \"INITIALIZED\";\n    LoaderStatus[LoaderStatus[\"LOADING\"] = 1] = \"LOADING\";\n    LoaderStatus[LoaderStatus[\"SUCCESS\"] = 2] = \"SUCCESS\";\n    LoaderStatus[LoaderStatus[\"FAILURE\"] = 3] = \"FAILURE\";\n})(LoaderStatus || (LoaderStatus = {}));\n/**\n * [[Loader]] makes it easier to add Google Maps JavaScript API to your application\n * dynamically using\n * [Promises](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise).\n * It works by dynamically creating and appending a script node to the the\n * document head and wrapping the callback function so as to return a promise.\n *\n * ```\n * const loader = new Loader({\n *   apiKey: \"\",\n *   version: \"weekly\",\n *   libraries: [\"places\"]\n * });\n *\n * loader.load().then((google) => {\n *   const map = new google.maps.Map(...)\n * })\n * ```\n */\nclass Loader {\n    /**\n     * Creates an instance of Loader using [[LoaderOptions]]. No defaults are set\n     * using this library, instead the defaults are set by the Google Maps\n     * JavaScript API server.\n     *\n     * ```\n     * const loader = Loader({apiKey, version: 'weekly', libraries: ['places']});\n     * ```\n     */\n    constructor({ apiKey, authReferrerPolicy, channel, client, id = DEFAULT_ID, language, libraries = [], mapIds, nonce, region, retries = 3, url = \"https://maps.googleapis.com/maps/api/js\", version, }) {\n        this.callbacks = [];\n        this.done = false;\n        this.loading = false;\n        this.errors = [];\n        this.apiKey = apiKey;\n        this.authReferrerPolicy = authReferrerPolicy;\n        this.channel = channel;\n        this.client = client;\n        this.id = id || DEFAULT_ID; // Do not allow empty string\n        this.language = language;\n        this.libraries = libraries;\n        this.mapIds = mapIds;\n        this.nonce = nonce;\n        this.region = region;\n        this.retries = retries;\n        this.url = url;\n        this.version = version;\n        if (Loader.instance) {\n            if (!isEqual(this.options, Loader.instance.options)) {\n                throw new Error(`Loader must not be called again with different options. ${JSON.stringify(this.options)} !== ${JSON.stringify(Loader.instance.options)}`);\n            }\n            return Loader.instance;\n        }\n        Loader.instance = this;\n    }\n    get options() {\n        return {\n            version: this.version,\n            apiKey: this.apiKey,\n            channel: this.channel,\n            client: this.client,\n            id: this.id,\n            libraries: this.libraries,\n            language: this.language,\n            region: this.region,\n            mapIds: this.mapIds,\n            nonce: this.nonce,\n            url: this.url,\n            authReferrerPolicy: this.authReferrerPolicy,\n        };\n    }\n    get status() {\n        if (this.errors.length) {\n            return LoaderStatus.FAILURE;\n        }\n        if (this.done) {\n            return LoaderStatus.SUCCESS;\n        }\n        if (this.loading) {\n            return LoaderStatus.LOADING;\n        }\n        return LoaderStatus.INITIALIZED;\n    }\n    get failed() {\n        return this.done && !this.loading && this.errors.length >= this.retries + 1;\n    }\n    /**\n     * CreateUrl returns the Google Maps JavaScript API script url given the [[LoaderOptions]].\n     *\n     * @ignore\n     * @deprecated\n     */\n    createUrl() {\n        let url = this.url;\n        url += `?callback=__googleMapsCallback&loading=async`;\n        if (this.apiKey) {\n            url += `&key=${this.apiKey}`;\n        }\n        if (this.channel) {\n            url += `&channel=${this.channel}`;\n        }\n        if (this.client) {\n            url += `&client=${this.client}`;\n        }\n        if (this.libraries.length > 0) {\n            url += `&libraries=${this.libraries.join(\",\")}`;\n        }\n        if (this.language) {\n            url += `&language=${this.language}`;\n        }\n        if (this.region) {\n            url += `&region=${this.region}`;\n        }\n        if (this.version) {\n            url += `&v=${this.version}`;\n        }\n        if (this.mapIds) {\n            url += `&map_ids=${this.mapIds.join(\",\")}`;\n        }\n        if (this.authReferrerPolicy) {\n            url += `&auth_referrer_policy=${this.authReferrerPolicy}`;\n        }\n        return url;\n    }\n    deleteScript() {\n        const script = document.getElementById(this.id);\n        if (script) {\n            script.remove();\n        }\n    }\n    /**\n     * Load the Google Maps JavaScript API script and return a Promise.\n     * @deprecated, use importLibrary() instead.\n     */\n    load() {\n        return this.loadPromise();\n    }\n    /**\n     * Load the Google Maps JavaScript API script and return a Promise.\n     *\n     * @ignore\n     * @deprecated, use importLibrary() instead.\n     */\n    loadPromise() {\n        return new Promise((resolve, reject) => {\n            this.loadCallback((err) => {\n                if (!err) {\n                    resolve(window.google);\n                }\n                else {\n                    reject(err.error);\n                }\n            });\n        });\n    }\n    importLibrary(name) {\n        this.execute();\n        return google.maps.importLibrary(name);\n    }\n    /**\n     * Load the Google Maps JavaScript API script with a callback.\n     * @deprecated, use importLibrary() instead.\n     */\n    loadCallback(fn) {\n        this.callbacks.push(fn);\n        this.execute();\n    }\n    /**\n     * Set the script on document.\n     */\n    setScript() {\n        var _a, _b;\n        if (document.getElementById(this.id)) {\n            // TODO wrap onerror callback for cases where the script was loaded elsewhere\n            this.callback();\n            return;\n        }\n        const params = {\n            key: this.apiKey,\n            channel: this.channel,\n            client: this.client,\n            libraries: this.libraries.length && this.libraries,\n            v: this.version,\n            mapIds: this.mapIds,\n            language: this.language,\n            region: this.region,\n            authReferrerPolicy: this.authReferrerPolicy,\n        };\n        // keep the URL minimal:\n        Object.keys(params).forEach(\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        (key) => !params[key] && delete params[key]);\n        if (!((_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.maps) === null || _b === void 0 ? void 0 : _b.importLibrary)) {\n            // tweaked copy of https://developers.google.com/maps/documentation/javascript/load-maps-js-api#dynamic-library-import\n            // which also sets the base url, the id, and the nonce\n            /* eslint-disable */\n            ((g) => {\n                // @ts-ignore\n                let h, a, k, p = \"The Google Maps JavaScript API\", c = \"google\", l = \"importLibrary\", q = \"__ib__\", m = document, b = window;\n                // @ts-ignore\n                b = b[c] || (b[c] = {});\n                // @ts-ignore\n                const d = b.maps || (b.maps = {}), r = new Set(), e = new URLSearchParams(), u = () => \n                // @ts-ignore\n                h || (h = new Promise((f, n) => __awaiter(this, void 0, void 0, function* () {\n                    var _a;\n                    yield (a = m.createElement(\"script\"));\n                    a.id = this.id;\n                    e.set(\"libraries\", [...r] + \"\");\n                    // @ts-ignore\n                    for (k in g)\n                        e.set(k.replace(/[A-Z]/g, (t) => \"_\" + t[0].toLowerCase()), g[k]);\n                    e.set(\"callback\", c + \".maps.\" + q);\n                    a.src = this.url + `?` + e;\n                    d[q] = f;\n                    a.onerror = () => (h = n(Error(p + \" could not load.\")));\n                    // @ts-ignore\n                    a.nonce = this.nonce || ((_a = m.querySelector(\"script[nonce]\")) === null || _a === void 0 ? void 0 : _a.nonce) || \"\";\n                    m.head.append(a);\n                })));\n                // @ts-ignore\n                d[l] ? console.warn(p + \" only loads once. Ignoring:\", g) : (d[l] = (f, ...n) => r.add(f) && u().then(() => d[l](f, ...n)));\n            })(params);\n            /* eslint-enable */\n        }\n        // While most libraries populate the global namespace when loaded via bootstrap params,\n        // this is not the case for \"marker\" when used with the inline bootstrap loader\n        // (and maybe others in the future). So ensure there is an importLibrary for each:\n        const libraryPromises = this.libraries.map((library) => this.importLibrary(library));\n        // ensure at least one library, to kick off loading...\n        if (!libraryPromises.length) {\n            libraryPromises.push(this.importLibrary(\"core\"));\n        }\n        Promise.all(libraryPromises).then(() => this.callback(), (error) => {\n            const event = new ErrorEvent(\"error\", { error }); // for backwards compat\n            this.loadErrorCallback(event);\n        });\n    }\n    /**\n     * Reset the loader state.\n     */\n    reset() {\n        this.deleteScript();\n        this.done = false;\n        this.loading = false;\n        this.errors = [];\n        this.onerrorEvent = null;\n    }\n    resetIfRetryingFailed() {\n        if (this.failed) {\n            this.reset();\n        }\n    }\n    loadErrorCallback(e) {\n        this.errors.push(e);\n        if (this.errors.length <= this.retries) {\n            const delay = this.errors.length * Math.pow(2, this.errors.length);\n            console.error(`Failed to load Google Maps script, retrying in ${delay} ms.`);\n            setTimeout(() => {\n                this.deleteScript();\n                this.setScript();\n            }, delay);\n        }\n        else {\n            this.onerrorEvent = e;\n            this.callback();\n        }\n    }\n    callback() {\n        this.done = true;\n        this.loading = false;\n        this.callbacks.forEach((cb) => {\n            cb(this.onerrorEvent);\n        });\n        this.callbacks = [];\n    }\n    execute() {\n        this.resetIfRetryingFailed();\n        if (this.loading) {\n            // do nothing but wait\n            return;\n        }\n        if (this.done) {\n            this.callback();\n        }\n        else {\n            // short circuit and warn if google.maps is already loaded\n            if (window.google && window.google.maps && window.google.maps.version) {\n                console.warn(\"Google Maps already loaded outside @googlemaps/js-api-loader. \" +\n                    \"This may result in undesirable behavior as options and script parameters may not match.\");\n                this.callback();\n                return;\n            }\n            this.loading = true;\n            this.setScript();\n        }\n    }\n}\n\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@googlemaps/js-api-loader/dist/index.mjs\n");

/***/ })

};
;