/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUtente%5CDocuments%5Caugment-projects%5Cwiki-city%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUtente%5CDocuments%5Caugment-projects%5Cwiki-city&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUtente%5CDocuments%5Caugment-projects%5Cwiki-city%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUtente%5CDocuments%5Caugment-projects%5Cwiki-city&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZwYWdlJnBhZ2U9JTJGcGFnZSZhcHBQYXRocz0lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGcGFnZS50c3gmYXBwRGlyPUMlM0ElNUNVc2VycyU1Q1V0ZW50ZSU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUN3aWtpLWNpdHklNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNVc2VycyU1Q1V0ZW50ZSU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUN3aWtpLWNpdHkmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLHNCQUFzQixvSkFBb0g7QUFDMUksc0JBQXNCLDBOQUFnRjtBQUN0RyxzQkFBc0IsME5BQWdGO0FBQ3RHLHNCQUFzQixnT0FBbUY7QUFDekcsb0JBQW9CLGdKQUFrSDtBQUdwSTtBQUdBO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQSxvQ0FBb0Msc2ZBQTRRO0FBQ2hUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxzZkFBNFE7QUFDaFQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDdUI7QUFHckI7QUFDRiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFHRTtBQUNGO0FBQ08sd0JBQXdCLHVHQUFrQjtBQUNqRDtBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBtb2R1bGUwID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxVdGVudGVcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcd2lraS1jaXR5XFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbmNvbnN0IG1vZHVsZTEgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIik7XG5jb25zdCBtb2R1bGUyID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZm9yYmlkZGVuLWVycm9yXCIpO1xuY29uc3QgbW9kdWxlMyA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3VuYXV0aG9yaXplZC1lcnJvclwiKTtcbmNvbnN0IHBhZ2U0ID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxVdGVudGVcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcd2lraS1jaXR5XFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG5pbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL2FwcC1wYWdlL21vZHVsZS5jb21waWxlZFwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNzcidcbn07XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc2VydmVyLXV0aWxpdHknXG59O1xuLy8gV2UgaW5qZWN0IHRoZSB0cmVlIGFuZCBwYWdlcyBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgdHJlZSA9IHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJycsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFsnX19QQUdFX18nLCB7fSwge1xuICAgICAgICAgIHBhZ2U6IFtwYWdlNCwgXCJDOlxcXFxVc2Vyc1xcXFxVdGVudGVcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcd2lraS1jaXR5XFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIl0sXG4gICAgICAgICAgbWV0YWRhdGE6IHtcbiAgICBpY29uOiBbKGFzeW5jIChwcm9wcykgPT4gKGF3YWl0IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC1tZXRhZGF0YS1pbWFnZS1sb2FkZXI/dHlwZT1mYXZpY29uJnNlZ21lbnQ9JmJhc2VQYXRoPSZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzIUM6XFxcXFVzZXJzXFxcXFV0ZW50ZVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFx3aWtpLWNpdHlcXFxcc3JjXFxcXGFwcFxcXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfX1wiKSkuZGVmYXVsdChwcm9wcykpXSxcbiAgICBhcHBsZTogW10sXG4gICAgb3BlbkdyYXBoOiBbXSxcbiAgICB0d2l0dGVyOiBbXSxcbiAgICBtYW5pZmVzdDogdW5kZWZpbmVkXG4gIH1cbiAgICAgICAgfV1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xheW91dCc6IFttb2R1bGUwLCBcIkM6XFxcXFVzZXJzXFxcXFV0ZW50ZVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFx3aWtpLWNpdHlcXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCJdLFxuJ25vdC1mb3VuZCc6IFttb2R1bGUxLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIl0sXG4nZm9yYmlkZGVuJzogW21vZHVsZTIsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2ZvcmJpZGRlbi1lcnJvclwiXSxcbid1bmF1dGhvcml6ZWQnOiBbbW9kdWxlMywgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvdW5hdXRob3JpemVkLWVycm9yXCJdLFxuICAgICAgICBtZXRhZGF0YToge1xuICAgIGljb246IFsoYXN5bmMgKHByb3BzKSA9PiAoYXdhaXQgaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlcj90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhQzpcXFxcVXNlcnNcXFxcVXRlbnRlXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXHdpa2ktY2l0eVxcXFxzcmNcXFxcYXBwXFxcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fXCIpKS5kZWZhdWx0KHByb3BzKSldLFxuICAgIGFwcGxlOiBbXSxcbiAgICBvcGVuR3JhcGg6IFtdLFxuICAgIHR3aXR0ZXI6IFtdLFxuICAgIG1hbmlmZXN0OiB1bmRlZmluZWRcbiAgfVxuICAgICAgfVxuICAgICAgXVxuICAgICAgfS5jaGlsZHJlbjtcbmNvbnN0IHBhZ2VzID0gW1wiQzpcXFxcVXNlcnNcXFxcVXRlbnRlXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXHdpa2ktY2l0eVxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCJdO1xuZXhwb3J0IHsgdHJlZSwgcGFnZXMgfTtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmFsRXJyb3IgfSBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5XCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc2VydmVyLXV0aWxpdHknXG59O1xuY29uc3QgX19uZXh0X2FwcF9yZXF1aXJlX18gPSBfX3dlYnBhY2tfcmVxdWlyZV9fXG5jb25zdCBfX25leHRfYXBwX2xvYWRfY2h1bmtfXyA9ICgpID0+IFByb21pc2UucmVzb2x2ZSgpXG5leHBvcnQgY29uc3QgX19uZXh0X2FwcF9fID0ge1xuICAgIHJlcXVpcmU6IF9fbmV4dF9hcHBfcmVxdWlyZV9fLFxuICAgIGxvYWRDaHVuazogX19uZXh0X2FwcF9sb2FkX2NodW5rX19cbn07XG5leHBvcnQgKiBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL2VudHJ5LWJhc2VcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBQYWdlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9QQUdFLFxuICAgICAgICBwYWdlOiBcIi9wYWdlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9cIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiAnJyxcbiAgICAgICAgZmlsZW5hbWU6ICcnLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9XG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXBhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUtente%5CDocuments%5Caugment-projects%5Cwiki-city%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUtente%5CDocuments%5Caugment-projects%5Cwiki-city&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Csrc%5C%5Ccomponents%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Csrc%5C%5Ccomponents%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SessionProvider.tsx */ \"(rsc)/./src/components/SessionProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Csrc%5C%5Ccomponents%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1V0ZW50ZSU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUN3aWtpLWNpdHklNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQWtIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxVdGVudGVcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcd2lraS1jaXR5XFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXRlbnRlXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXHdpa2ktY2l0eVxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFV0ZW50ZVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFx3aWtpLWNpdHlcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjcxOWNiMGZjM2Y2M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_SessionProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/SessionProvider */ \"(rsc)/./src/components/SessionProvider.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"WikiCity - Discover Points of Interest\",\n    description: \"Discover and explore points of interest in any location with Wikipedia integration and Google Maps\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SessionProvider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\wiki-city\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/SessionProvider.tsx":
/*!********************************************!*\
  !*** ./src/components/SessionProvider.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\wiki-city\\src\\components\\SessionProvider.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Csrc%5C%5Ccomponents%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Csrc%5C%5Ccomponents%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SessionProvider.tsx */ \"(ssr)/./src/components/SessionProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Csrc%5C%5Ccomponents%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1V0ZW50ZSU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUN3aWtpLWNpdHklNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQWtIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxVdGVudGVcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcd2lraS1jaXR5XFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDocuments%5C%5Caugment-projects%5C%5Cwiki-city%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_MapComponent__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/MapComponent */ \"(ssr)/./src/components/MapComponent.tsx\");\n/* harmony import */ var _components_LocationSearch__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/LocationSearch */ \"(ssr)/./src/components/LocationSearch.tsx\");\n/* harmony import */ var _components_LocationInfo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/LocationInfo */ \"(ssr)/./src/components/LocationInfo.tsx\");\n/* harmony import */ var _components_SavedLocations__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/SavedLocations */ \"(ssr)/./src/components/SavedLocations.tsx\");\n/* harmony import */ var _barrel_optimize_names_Heart_LogOut_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,LogOut,MapPin,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_LogOut_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,LogOut,MapPin,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_LogOut_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,LogOut,MapPin,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_LogOut_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,LogOut,MapPin,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction Home() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [selectedLocation, setSelectedLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showSavedLocations, setShowSavedLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userLocation, setUserLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            // Get user's current location\n            if (navigator.geolocation) {\n                navigator.geolocation.getCurrentPosition({\n                    \"Home.useEffect\": (position)=>{\n                        setUserLocation({\n                            lat: position.coords.latitude,\n                            lng: position.coords.longitude\n                        });\n                    }\n                }[\"Home.useEffect\"], {\n                    \"Home.useEffect\": (error)=>{\n                        console.error('Error getting location:', error);\n                        // Default to New York City if location access is denied\n                        setUserLocation({\n                            lat: 40.7128,\n                            lng: -74.0060\n                        });\n                    }\n                }[\"Home.useEffect\"]);\n            } else {\n                // Default to New York City if geolocation is not supported\n                setUserLocation({\n                    lat: 40.7128,\n                    lng: -74.0060\n                });\n            }\n        }\n    }[\"Home.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_LogOut_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"WikiCity\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: session ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowSavedLocations(!showSavedLocations),\n                                            className: \"flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_LogOut_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 57,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Saved\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 58,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_LogOut_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-700\",\n                                                    children: session.user?.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signOut)(),\n                                            className: \"flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_LogOut_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Sign Out\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signIn)('google'),\n                                    className: \"flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_LogOut_MapPin_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Sign In with Google\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-8rem)]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LocationSearch__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    onLocationSelect: setSelectedLocation,\n                                    userLocation: userLocation\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                selectedLocation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LocationInfo__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    location: selectedLocation,\n                                    session: session\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, this),\n                                showSavedLocations && session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SavedLocations__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    session: session,\n                                    onLocationSelect: setSelectedLocation\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full rounded-lg overflow-hidden shadow-lg\",\n                                children: userLocation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MapComponent__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    center: userLocation,\n                                    onLocationSelect: setSelectedLocation,\n                                    selectedLocation: selectedLocation\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/LocationInfo.tsx":
/*!*****************************************!*\
  !*** ./src/components/LocationInfo.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LocationInfo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Heart_MapPin_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Heart,MapPin,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Heart_MapPin_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Heart,MapPin,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Heart_MapPin_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Heart,MapPin,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Heart_MapPin_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Heart,MapPin,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction LocationInfo({ location, session }) {\n    const [wikipediaInfo, setWikipediaInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingWikipedia, setIsLoadingWikipedia] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSaved, setIsSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoadingSave, setIsLoadingSave] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LocationInfo.useEffect\": ()=>{\n            if (location) {\n                fetchWikipediaInfo();\n                if (session) {\n                    checkIfSaved();\n                }\n            }\n        }\n    }[\"LocationInfo.useEffect\"], [\n        location,\n        session\n    ]);\n    const fetchWikipediaInfo = async ()=>{\n        setIsLoadingWikipedia(true);\n        try {\n            // Search Wikipedia for information about the location\n            const searchQuery = location.name;\n            const searchResponse = await fetch(`https://en.wikipedia.org/api/rest_v1/page/summary/${encodeURIComponent(searchQuery)}`);\n            if (searchResponse.ok) {\n                const data = await searchResponse.json();\n                setWikipediaInfo(data);\n            } else {\n                // If direct search fails, try a more general search\n                const generalSearchResponse = await fetch(`https://en.wikipedia.org/w/api.php?action=opensearch&search=${encodeURIComponent(searchQuery)}&limit=1&namespace=0&format=json&origin=*`);\n                if (generalSearchResponse.ok) {\n                    const [, titles, descriptions, urls] = await generalSearchResponse.json();\n                    if (titles.length > 0) {\n                        setWikipediaInfo({\n                            title: titles[0],\n                            extract: descriptions[0],\n                            content_urls: {\n                                desktop: {\n                                    page: urls[0]\n                                }\n                            }\n                        });\n                    }\n                }\n            }\n        } catch (error) {\n            console.error('Error fetching Wikipedia info:', error);\n        } finally{\n            setIsLoadingWikipedia(false);\n        }\n    };\n    const checkIfSaved = async ()=>{\n        if (!session?.user?.email) return;\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('saved_locations').select('id').eq('user_email', session.user.email).eq('place_id', location.place_id).single();\n            setIsSaved(!!data);\n        } catch (error) {\n            console.error('Error checking if location is saved:', error);\n        }\n    };\n    const handleSaveLocation = async ()=>{\n        if (!session?.user?.email) return;\n        setIsLoadingSave(true);\n        try {\n            if (isSaved) {\n                // Remove from saved locations\n                const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('saved_locations').delete().eq('user_email', session.user.email).eq('place_id', location.place_id);\n                if (!error) {\n                    setIsSaved(false);\n                }\n            } else {\n                // Add to saved locations\n                const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('saved_locations').insert({\n                    user_email: session.user.email,\n                    place_id: location.place_id,\n                    name: location.name,\n                    location: location.location,\n                    formatted_address: location.formatted_address,\n                    types: location.types,\n                    rating: location.rating,\n                    vicinity: location.vicinity\n                });\n                if (!error) {\n                    setIsSaved(true);\n                }\n            }\n        } catch (error) {\n            console.error('Error saving/removing location:', error);\n        } finally{\n            setIsLoadingSave(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900\",\n                                children: location.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationInfo.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this),\n                            location.formatted_address && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mt-1 text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Heart_MapPin_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationInfo.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: location.formatted_address\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationInfo.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationInfo.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationInfo.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleSaveLocation,\n                        disabled: isLoadingSave,\n                        className: `p-2 rounded-full transition-colors ${isSaved ? 'text-red-600 bg-red-50 hover:bg-red-100' : 'text-gray-400 bg-gray-50 hover:bg-gray-100'}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Heart_MapPin_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: `h-5 w-5 ${isSaved ? 'fill-current' : ''}`\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationInfo.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationInfo.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationInfo.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this),\n            location.rating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center mb-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            ...Array(5)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Heart_MapPin_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: `h-4 w-4 ${i < Math.floor(location.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`\n                            }, i, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationInfo.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationInfo.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 text-sm text-gray-600\",\n                        children: location.rating.toFixed(1)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationInfo.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationInfo.tsx\",\n                lineNumber: 157,\n                columnNumber: 9\n            }, this),\n            location.types && location.types.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-1\",\n                    children: location.types.slice(0, 3).map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"px-2 py-1 text-xs font-medium text-blue-600 bg-blue-50 rounded-full\",\n                            children: type.replace(/_/g, ' ').replace(/\\b\\w/g, (l)=>l.toUpperCase())\n                        }, type, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationInfo.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationInfo.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationInfo.tsx\",\n                lineNumber: 178,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t pt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-md font-medium text-gray-900 mb-2\",\n                        children: \"About this place\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationInfo.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, this),\n                    isLoadingWikipedia ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationInfo.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-sm text-gray-600\",\n                                children: \"Loading information...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationInfo.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationInfo.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 11\n                    }, this) : wikipediaInfo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-700 mb-3 line-clamp-4\",\n                                children: wikipediaInfo.extract || 'No description available.'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationInfo.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this),\n                            wikipediaInfo.content_urls?.desktop?.page && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: wikipediaInfo.content_urls.desktop.page,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"inline-flex items-center text-sm text-blue-600 hover:text-blue-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Heart_MapPin_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationInfo.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Read more on Wikipedia\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationInfo.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationInfo.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"No additional information available for this location.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationInfo.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationInfo.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationInfo.tsx\",\n        lineNumber: 128,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LocationInfo.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/LocationSearch.tsx":
/*!*******************************************!*\
  !*** ./src/components/LocationSearch.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LocationSearch)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_MapPin_Navigation_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=MapPin,Navigation,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_MapPin_Navigation_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=MapPin,Navigation,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_MapPin_Navigation_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=MapPin,Navigation,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction LocationSearch({ onLocationSelect, userLocation }) {\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [autocompleteService, setAutocompleteService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [placesService, setPlacesService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LocationSearch.useEffect\": ()=>{\n            // Initialize Google Places services\n            if (false) {}\n        }\n    }[\"LocationSearch.useEffect\"], []);\n    const handleSearch = async (query)=>{\n        if (!query.trim() || !autocompleteService) return;\n        setIsLoading(true);\n        const request = {\n            input: query,\n            location: userLocation ? new google.maps.LatLng(userLocation.lat, userLocation.lng) : undefined,\n            radius: userLocation ? 50000 : undefined\n        };\n        autocompleteService.getPlacePredictions(request, (predictions, status)=>{\n            setIsLoading(false);\n            if (status === google.maps.places.PlacesServiceStatus.OK && predictions) {\n                setSearchResults(predictions.slice(0, 5));\n            } else {\n                setSearchResults([]);\n            }\n        });\n    };\n    const handlePlaceSelect = (placeId)=>{\n        if (!placesService) return;\n        const request = {\n            placeId: placeId,\n            fields: [\n                'place_id',\n                'name',\n                'geometry',\n                'formatted_address',\n                'types',\n                'rating',\n                'vicinity'\n            ]\n        };\n        placesService.getDetails(request, (place, status)=>{\n            if (status === google.maps.places.PlacesServiceStatus.OK && place) {\n                onLocationSelect({\n                    place_id: place.place_id,\n                    name: place.name,\n                    location: {\n                        lat: place.geometry.location.lat(),\n                        lng: place.geometry.location.lng()\n                    },\n                    formatted_address: place.formatted_address,\n                    types: place.types,\n                    rating: place.rating,\n                    vicinity: place.vicinity\n                });\n                setSearchQuery('');\n                setSearchResults([]);\n            }\n        });\n    };\n    const handleCurrentLocation = ()=>{\n        if (navigator.geolocation) {\n            navigator.geolocation.getCurrentPosition((position)=>{\n                const location = {\n                    lat: position.coords.latitude,\n                    lng: position.coords.longitude\n                };\n                onLocationSelect({\n                    name: 'Current Location',\n                    location,\n                    formatted_address: 'Your current location',\n                    types: [\n                        'current_location'\n                    ]\n                });\n            }, (error)=>{\n                console.error('Error getting current location:', error);\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LocationSearch.useEffect\": ()=>{\n            const timeoutId = setTimeout({\n                \"LocationSearch.useEffect.timeoutId\": ()=>{\n                    if (searchQuery) {\n                        handleSearch(searchQuery);\n                    } else {\n                        setSearchResults([]);\n                    }\n                }\n            }[\"LocationSearch.useEffect.timeoutId\"], 300);\n            return ({\n                \"LocationSearch.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"LocationSearch.useEffect\"];\n        }\n    }[\"LocationSearch.useEffect\"], [\n        searchQuery,\n        autocompleteService,\n        userLocation\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                children: \"Search Locations\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationSearch.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MapPin_Navigation_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationSearch.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Search for places...\",\n                                value: searchQuery,\n                                onChange: (e)=>setSearchQuery(e.target.value),\n                                className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationSearch.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationSearch.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this),\n                    searchResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto\",\n                        children: searchResults.map((result)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handlePlaceSelect(result.place_id),\n                                className: \"w-full px-4 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none border-b border-gray-100 last:border-b-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MapPin_Navigation_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-4 w-4 text-gray-400 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationSearch.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-900 truncate\",\n                                                    children: result.structured_formatting?.main_text || result.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationSearch.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 truncate\",\n                                                    children: result.structured_formatting?.secondary_text || ''\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationSearch.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationSearch.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationSearch.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 17\n                                }, this)\n                            }, result.place_id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationSearch.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationSearch.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationSearch.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 text-sm text-gray-600\",\n                                    children: \"Searching...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationSearch.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationSearch.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationSearch.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationSearch.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleCurrentLocation,\n                className: \"w-full mt-4 flex items-center justify-center space-x-2 px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MapPin_Navigation_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationSearch.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Use Current Location\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationSearch.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationSearch.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\LocationSearch.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LocationSearch.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/MapComponent.tsx":
/*!*****************************************!*\
  !*** ./src/components/MapComponent.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MapComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _googlemaps_js_api_loader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @googlemaps/js-api-loader */ \"(ssr)/./node_modules/@googlemaps/js-api-loader/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction MapComponent({ center, onLocationSelect, selectedLocation }) {\n    const mapRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [map, setMap] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [markers, setMarkers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [placesService, setPlacesService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MapComponent.useEffect\": ()=>{\n            const initMap = {\n                \"MapComponent.useEffect.initMap\": async ()=>{\n                    const loader = new _googlemaps_js_api_loader__WEBPACK_IMPORTED_MODULE_2__.Loader({\n                        apiKey: \"AIzaSyCCjPAakviEk74erNNO0qvDivSNVo130hQ\" || 0,\n                        version: 'weekly',\n                        libraries: [\n                            'places'\n                        ]\n                    });\n                    try {\n                        await loader.load();\n                        if (mapRef.current) {\n                            const mapInstance = new google.maps.Map(mapRef.current, {\n                                center,\n                                zoom: 14,\n                                styles: [\n                                    {\n                                        featureType: 'poi',\n                                        elementType: 'labels',\n                                        stylers: [\n                                            {\n                                                visibility: 'on'\n                                            }\n                                        ]\n                                    }\n                                ]\n                            });\n                            setMap(mapInstance);\n                            setPlacesService(new google.maps.places.PlacesService(mapInstance));\n                            // Add user location marker\n                            new google.maps.Marker({\n                                position: center,\n                                map: mapInstance,\n                                title: 'Your Location',\n                                icon: {\n                                    url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <circle cx=\"12\" cy=\"12\" r=\"8\" fill=\"#3B82F6\" stroke=\"#FFFFFF\" stroke-width=\"2\"/>\n                  <circle cx=\"12\" cy=\"12\" r=\"3\" fill=\"#FFFFFF\"/>\n                </svg>\n              `),\n                                    scaledSize: new google.maps.Size(24, 24)\n                                }\n                            });\n                            // Search for nearby points of interest\n                            searchNearbyPlaces(mapInstance, center);\n                        }\n                    } catch (error) {\n                        console.error('Error loading Google Maps:', error);\n                    }\n                }\n            }[\"MapComponent.useEffect.initMap\"];\n            initMap();\n        }\n    }[\"MapComponent.useEffect\"], [\n        center\n    ]);\n    const searchNearbyPlaces = (mapInstance, location)=>{\n        if (!placesService) return;\n        const request = {\n            location: new google.maps.LatLng(location.lat, location.lng),\n            radius: 2000,\n            type: 'point_of_interest'\n        };\n        placesService.nearbySearch(request, (results, status)=>{\n            if (status === google.maps.places.PlacesServiceStatus.OK && results) {\n                // Clear existing markers\n                markers.forEach((marker)=>marker.setMap(null));\n                const newMarkers = [];\n                results.slice(0, 20).forEach((place)=>{\n                    if (place.geometry?.location) {\n                        const marker = new google.maps.Marker({\n                            position: place.geometry.location,\n                            map: mapInstance,\n                            title: place.name,\n                            icon: {\n                                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`\n                  <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path d=\"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\" fill=\"#EF4444\" stroke=\"#FFFFFF\" stroke-width=\"2\"/>\n                    <circle cx=\"12\" cy=\"10\" r=\"3\" fill=\"#FFFFFF\"/>\n                  </svg>\n                `),\n                                scaledSize: new google.maps.Size(20, 20)\n                            }\n                        });\n                        marker.addListener('click', ()=>{\n                            onLocationSelect({\n                                place_id: place.place_id,\n                                name: place.name,\n                                location: {\n                                    lat: place.geometry.location.lat(),\n                                    lng: place.geometry.location.lng()\n                                },\n                                types: place.types,\n                                rating: place.rating,\n                                vicinity: place.vicinity\n                            });\n                        });\n                        newMarkers.push(marker);\n                    }\n                });\n                setMarkers(newMarkers);\n            }\n        });\n    };\n    // Update map center when center prop changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MapComponent.useEffect\": ()=>{\n            if (map) {\n                map.setCenter(center);\n                searchNearbyPlaces(map, center);\n            }\n        }\n    }[\"MapComponent.useEffect\"], [\n        center,\n        map,\n        placesService\n    ]);\n    // Highlight selected location\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MapComponent.useEffect\": ()=>{\n            if (selectedLocation && map) {\n                map.setCenter(selectedLocation.location);\n                map.setZoom(16);\n            }\n        }\n    }[\"MapComponent.useEffect\"], [\n        selectedLocation,\n        map\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: mapRef,\n            className: \"w-full h-full\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\MapComponent.tsx\",\n            lineNumber: 145,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\MapComponent.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MapComponent.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SavedLocations.tsx":
/*!*******************************************!*\
  !*** ./src/components/SavedLocations.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SavedLocations)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Heart_MapPin_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,MapPin,Star,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_MapPin_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,MapPin,Star,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_MapPin_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,MapPin,Star,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_MapPin_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,MapPin,Star,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction SavedLocations({ session, onLocationSelect }) {\n    const [savedLocations, setSavedLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SavedLocations.useEffect\": ()=>{\n            fetchSavedLocations();\n        }\n    }[\"SavedLocations.useEffect\"], [\n        session\n    ]);\n    const fetchSavedLocations = async ()=>{\n        if (!session?.user?.email) return;\n        setIsLoading(true);\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('saved_locations').select('*').eq('user_email', session.user.email).order('created_at', {\n                ascending: false\n            });\n            if (error) {\n                console.error('Error fetching saved locations:', error);\n            } else {\n                setSavedLocations(data || []);\n            }\n        } catch (error) {\n            console.error('Error fetching saved locations:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleRemoveLocation = async (locationId)=>{\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('saved_locations').delete().eq('id', locationId);\n            if (!error) {\n                setSavedLocations((prev)=>prev.filter((loc)=>loc.id !== locationId));\n            }\n        } catch (error) {\n            console.error('Error removing saved location:', error);\n        }\n    };\n    const handleLocationClick = (location)=>{\n        onLocationSelect({\n            place_id: location.place_id,\n            name: location.name,\n            location: location.location,\n            formatted_address: location.formatted_address,\n            types: location.types,\n            rating: location.rating,\n            vicinity: location.vicinity\n        });\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MapPin_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"h-5 w-5 mr-2 text-red-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SavedLocations.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        \"Saved Locations\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SavedLocations.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SavedLocations.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 text-sm text-gray-600\",\n                            children: \"Loading saved locations...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SavedLocations.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SavedLocations.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SavedLocations.tsx\",\n            lineNumber: 73,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MapPin_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"h-5 w-5 mr-2 text-red-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SavedLocations.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this),\n                    \"Saved Locations\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SavedLocations.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            savedLocations.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MapPin_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"h-12 w-12 text-gray-300 mx-auto mb-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SavedLocations.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"No saved locations yet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SavedLocations.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-400 mt-1\",\n                        children: \"Click the heart icon on any location to save it\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SavedLocations.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SavedLocations.tsx\",\n                lineNumber: 94,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3 max-h-96 overflow-y-auto\",\n                children: savedLocations.map((location)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border border-gray-200 rounded-lg p-3 hover:bg-gray-50 transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleLocationClick(location),\n                                    className: \"flex-1 text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-gray-900 text-sm mb-1\",\n                                            children: location.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SavedLocations.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 19\n                                        }, this),\n                                        location.formatted_address && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-xs text-gray-600 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MapPin_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SavedLocations.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"truncate\",\n                                                    children: location.formatted_address\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SavedLocations.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SavedLocations.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 21\n                                        }, this),\n                                        location.rating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        ...Array(5)\n                                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MapPin_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: `h-3 w-3 ${i < Math.floor(location.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`\n                                                        }, i, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SavedLocations.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SavedLocations.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-1 text-xs text-gray-600\",\n                                                    children: location.rating.toFixed(1)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SavedLocations.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SavedLocations.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 21\n                                        }, this),\n                                        location.types && location.types.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-1\",\n                                            children: location.types.slice(0, 2).map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 py-0.5 text-xs font-medium text-blue-600 bg-blue-50 rounded-full\",\n                                                    children: type.replace(/_/g, ' ').replace(/\\b\\w/g, (l)=>l.toUpperCase())\n                                                }, type, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SavedLocations.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SavedLocations.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SavedLocations.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleRemoveLocation(location.id),\n                                    className: \"ml-2 p-1 text-gray-400 hover:text-red-600 transition-colors\",\n                                    title: \"Remove from saved locations\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MapPin_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SavedLocations.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SavedLocations.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SavedLocations.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 15\n                        }, this)\n                    }, location.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SavedLocations.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SavedLocations.tsx\",\n                lineNumber: 102,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SavedLocations.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SavedLocations.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SessionProvider.tsx":
/*!********************************************!*\
  !*** ./src/components/SessionProvider.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction SessionProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\SessionProvider.tsx\",\n        lineNumber: 11,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9TZXNzaW9uUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUU2RTtBQU85RCxTQUFTQSxnQkFBZ0IsRUFBRUUsUUFBUSxFQUF3QjtJQUN4RSxxQkFBTyw4REFBQ0QsNERBQXVCQTtrQkFBRUM7Ozs7OztBQUNuQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVdGVudGVcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcd2lraS1jaXR5XFxzcmNcXGNvbXBvbmVudHNcXFNlc3Npb25Qcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBTZXNzaW9uUHJvdmlkZXIgYXMgTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXIgfSBmcm9tICduZXh0LWF1dGgvcmVhY3QnO1xuaW1wb3J0IHsgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnO1xuXG5pbnRlcmZhY2UgU2Vzc2lvblByb3ZpZGVyUHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3ROb2RlO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTZXNzaW9uUHJvdmlkZXIoeyBjaGlsZHJlbiB9OiBTZXNzaW9uUHJvdmlkZXJQcm9wcykge1xuICByZXR1cm4gPE5leHRBdXRoU2Vzc2lvblByb3ZpZGVyPntjaGlsZHJlbn08L05leHRBdXRoU2Vzc2lvblByb3ZpZGVyPjtcbn1cbiJdLCJuYW1lcyI6WyJTZXNzaW9uUHJvdmlkZXIiLCJOZXh0QXV0aFNlc3Npb25Qcm92aWRlciIsImNoaWxkcmVuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SessionProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://placeholder.supabase.co\" || 0;\nconst supabaseAnonKey = \"placeholder_supabase_anon_key\" || 0;\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3N1cGFiYXNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXFEO0FBRXJELE1BQU1DLGNBQWNDLGlDQUFvQyxJQUFJLENBQWlDO0FBQzdGLE1BQU1HLGtCQUFrQkgsK0JBQXlDLElBQUksQ0FBaUI7QUFFL0UsTUFBTUssV0FBV1AsbUVBQVlBLENBQUNDLGFBQWFJLGlCQUFpQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVdGVudGVcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcd2lraS1jaXR5XFxzcmNcXGxpYlxcc3VwYWJhc2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJztcblxuY29uc3Qgc3VwYWJhc2VVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwgfHwgJ2h0dHBzOi8vcGxhY2Vob2xkZXIuc3VwYWJhc2UuY28nO1xuY29uc3Qgc3VwYWJhc2VBbm9uS2V5ID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkgfHwgJ3BsYWNlaG9sZGVyX2tleSc7XG5cbmV4cG9ydCBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudChzdXBhYmFzZVVybCwgc3VwYWJhc2VBbm9uS2V5KTtcblxuLy8gRGF0YWJhc2UgdHlwZXNcbmV4cG9ydCBpbnRlcmZhY2UgU2F2ZWRMb2NhdGlvbiB7XG4gIGlkOiBzdHJpbmc7XG4gIHVzZXJfZW1haWw6IHN0cmluZztcbiAgcGxhY2VfaWQ6IHN0cmluZztcbiAgbmFtZTogc3RyaW5nO1xuICBsb2NhdGlvbjoge1xuICAgIGxhdDogbnVtYmVyO1xuICAgIGxuZzogbnVtYmVyO1xuICB9O1xuICBmb3JtYXR0ZWRfYWRkcmVzcz86IHN0cmluZztcbiAgdHlwZXM/OiBzdHJpbmdbXTtcbiAgcmF0aW5nPzogbnVtYmVyO1xuICB2aWNpbml0eT86IHN0cmluZztcbiAgY3JlYXRlZF9hdDogc3RyaW5nO1xuICB1cGRhdGVkX2F0OiBzdHJpbmc7XG59XG4iXSwibmFtZXMiOlsiY3JlYXRlQ2xpZW50Iiwic3VwYWJhc2VVcmwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwic3VwYWJhc2VBbm9uS2V5IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkiLCJzdXBhYmFzZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@supabase","vendor-chunks/ws","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/whatwg-url","vendor-chunks/tr46","vendor-chunks/@googlemaps","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUtente%5CDocuments%5Caugment-projects%5Cwiki-city%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUtente%5CDocuments%5Caugment-projects%5Cwiki-city&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();