'use client';

import { useState, useEffect } from 'react';
import { Session } from 'next-auth';
import { Star, Heart, ExternalLink, MapPin, Clock, Phone, Globe } from 'lucide-react';
import { supabase } from '@/lib/supabase';

interface LocationInfoProps {
  location: any;
  session: Session | null;
}

export default function LocationInfo({ location, session }: LocationInfoProps) {
  const [wikipediaInfo, setWikipediaInfo] = useState<any>(null);
  const [isLoadingWikipedia, setIsLoadingWikipedia] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [isLoadingSave, setIsLoadingSave] = useState(false);

  useEffect(() => {
    if (location) {
      fetchWikipediaInfo();
      if (session) {
        checkIfSaved();
      }
    }
  }, [location, session]);

  const fetchWikipediaInfo = async () => {
    setIsLoadingWikipedia(true);
    try {
      // Search Wikipedia for information about the location
      const searchQuery = location.name;
      const searchResponse = await fetch(
        `https://en.wikipedia.org/api/rest_v1/page/summary/${encodeURIComponent(searchQuery)}`
      );

      if (searchResponse.ok) {
        const data = await searchResponse.json();
        setWikipediaInfo(data);
      } else {
        // If direct search fails, try a more general search
        const generalSearchResponse = await fetch(
          `https://en.wikipedia.org/w/api.php?action=opensearch&search=${encodeURIComponent(searchQuery)}&limit=1&namespace=0&format=json&origin=*`
        );
        
        if (generalSearchResponse.ok) {
          const [, titles, descriptions, urls] = await generalSearchResponse.json();
          if (titles.length > 0) {
            setWikipediaInfo({
              title: titles[0],
              extract: descriptions[0],
              content_urls: {
                desktop: {
                  page: urls[0]
                }
              }
            });
          }
        }
      }
    } catch (error) {
      console.error('Error fetching Wikipedia info:', error);
    } finally {
      setIsLoadingWikipedia(false);
    }
  };

  const checkIfSaved = async () => {
    if (!session?.user?.email) return;

    try {
      const { data, error } = await supabase
        .from('saved_locations')
        .select('id')
        .eq('user_email', session.user.email)
        .eq('place_id', location.place_id)
        .single();

      setIsSaved(!!data);
    } catch (error) {
      console.error('Error checking if location is saved:', error);
    }
  };

  const handleSaveLocation = async () => {
    if (!session?.user?.email) return;

    setIsLoadingSave(true);
    try {
      if (isSaved) {
        // Remove from saved locations
        const { error } = await supabase
          .from('saved_locations')
          .delete()
          .eq('user_email', session.user.email)
          .eq('place_id', location.place_id);

        if (!error) {
          setIsSaved(false);
        }
      } else {
        // Add to saved locations
        const { error } = await supabase
          .from('saved_locations')
          .insert({
            user_email: session.user.email,
            place_id: location.place_id,
            name: location.name,
            location: location.location,
            formatted_address: location.formatted_address,
            types: location.types,
            rating: location.rating,
            vicinity: location.vicinity,
          });

        if (!error) {
          setIsSaved(true);
        }
      }
    } catch (error) {
      console.error('Error saving/removing location:', error);
    } finally {
      setIsLoadingSave(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900">{location.name}</h3>
          {location.formatted_address && (
            <div className="flex items-center mt-1 text-sm text-gray-600">
              <MapPin className="h-4 w-4 mr-1" />
              <span>{location.formatted_address}</span>
            </div>
          )}
        </div>
        
        {session && (
          <button
            onClick={handleSaveLocation}
            disabled={isLoadingSave}
            className={`p-2 rounded-full transition-colors ${
              isSaved
                ? 'text-red-600 bg-red-50 hover:bg-red-100'
                : 'text-gray-400 bg-gray-50 hover:bg-gray-100'
            }`}
          >
            <Heart className={`h-5 w-5 ${isSaved ? 'fill-current' : ''}`} />
          </button>
        )}
      </div>

      {/* Rating */}
      {location.rating && (
        <div className="flex items-center mb-3">
          <div className="flex items-center">
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={`h-4 w-4 ${
                  i < Math.floor(location.rating)
                    ? 'text-yellow-400 fill-current'
                    : 'text-gray-300'
                }`}
              />
            ))}
          </div>
          <span className="ml-2 text-sm text-gray-600">
            {location.rating.toFixed(1)}
          </span>
        </div>
      )}

      {/* Types/Categories */}
      {location.types && location.types.length > 0 && (
        <div className="mb-4">
          <div className="flex flex-wrap gap-1">
            {location.types.slice(0, 3).map((type: string) => (
              <span
                key={type}
                className="px-2 py-1 text-xs font-medium text-blue-600 bg-blue-50 rounded-full"
              >
                {type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Wikipedia Information */}
      <div className="border-t pt-4">
        <h4 className="text-md font-medium text-gray-900 mb-2">About this place</h4>
        
        {isLoadingWikipedia ? (
          <div className="flex items-center justify-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-sm text-gray-600">Loading information...</span>
          </div>
        ) : wikipediaInfo ? (
          <div>
            <p className="text-sm text-gray-700 mb-3 line-clamp-4">
              {wikipediaInfo.extract || 'No description available.'}
            </p>
            
            {wikipediaInfo.content_urls?.desktop?.page && (
              <a
                href={wikipediaInfo.content_urls.desktop.page}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800"
              >
                <ExternalLink className="h-4 w-4 mr-1" />
                Read more on Wikipedia
              </a>
            )}
          </div>
        ) : (
          <p className="text-sm text-gray-500">
            No additional information available for this location.
          </p>
        )}
      </div>
    </div>
  );
}
