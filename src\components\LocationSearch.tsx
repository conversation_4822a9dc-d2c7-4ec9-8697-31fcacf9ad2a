'use client';

import { useState, useEffect } from 'react';
import { Search, MapPin, Navigation } from 'lucide-react';

interface LocationSearchProps {
  onLocationSelect: (location: any) => void;
  userLocation: { lat: number; lng: number } | null;
}

export default function LocationSearch({ onLocationSelect, userLocation }: LocationSearchProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [autocompleteService, setAutocompleteService] = useState<google.maps.places.AutocompleteService | null>(null);
  const [placesService, setPlacesService] = useState<google.maps.places.PlacesService | null>(null);

  useEffect(() => {
    // Initialize Google Places services
    if (typeof window !== 'undefined' && window.google) {
      setAutocompleteService(new google.maps.places.AutocompleteService());
      
      // Create a dummy map for PlacesService (required by Google Maps API)
      const dummyMap = new google.maps.Map(document.createElement('div'));
      setPlacesService(new google.maps.places.PlacesService(dummyMap));
    }
  }, []);

  const handleSearch = async (query: string) => {
    if (!query.trim() || !autocompleteService) return;

    setIsLoading(true);
    
    const request = {
      input: query,
      location: userLocation ? new google.maps.LatLng(userLocation.lat, userLocation.lng) : undefined,
      radius: userLocation ? 50000 : undefined, // 50km radius
    };

    autocompleteService.getPlacePredictions(request, (predictions, status) => {
      setIsLoading(false);
      
      if (status === google.maps.places.PlacesServiceStatus.OK && predictions) {
        setSearchResults(predictions.slice(0, 5));
      } else {
        setSearchResults([]);
      }
    });
  };

  const handlePlaceSelect = (placeId: string) => {
    if (!placesService) return;

    const request = {
      placeId: placeId,
      fields: ['place_id', 'name', 'geometry', 'formatted_address', 'types', 'rating', 'vicinity'],
    };

    placesService.getDetails(request, (place, status) => {
      if (status === google.maps.places.PlacesServiceStatus.OK && place) {
        onLocationSelect({
          place_id: place.place_id,
          name: place.name,
          location: {
            lat: place.geometry!.location!.lat(),
            lng: place.geometry!.location!.lng(),
          },
          formatted_address: place.formatted_address,
          types: place.types,
          rating: place.rating,
          vicinity: place.vicinity,
        });
        
        setSearchQuery('');
        setSearchResults([]);
      }
    });
  };

  const handleCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const location = {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          };
          
          onLocationSelect({
            name: 'Current Location',
            location,
            formatted_address: 'Your current location',
            types: ['current_location'],
          });
        },
        (error) => {
          console.error('Error getting current location:', error);
        }
      );
    }
  };

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchQuery) {
        handleSearch(searchQuery);
      } else {
        setSearchResults([]);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery, autocompleteService, userLocation]);

  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      <h2 className="text-lg font-semibold text-gray-900 mb-4">Search Locations</h2>
      
      <div className="relative">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search for places..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Search Results */}
        {searchResults.length > 0 && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
            {searchResults.map((result) => (
              <button
                key={result.place_id}
                onClick={() => handlePlaceSelect(result.place_id)}
                className="w-full px-4 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none border-b border-gray-100 last:border-b-0"
              >
                <div className="flex items-center space-x-2">
                  <MapPin className="h-4 w-4 text-gray-400 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {result.structured_formatting?.main_text || result.description}
                    </p>
                    <p className="text-xs text-gray-500 truncate">
                      {result.structured_formatting?.secondary_text || ''}
                    </p>
                  </div>
                </div>
              </button>
            ))}
          </div>
        )}

        {isLoading && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg p-4">
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-sm text-gray-600">Searching...</span>
            </div>
          </div>
        )}
      </div>

      {/* Current Location Button */}
      <button
        onClick={handleCurrentLocation}
        className="w-full mt-4 flex items-center justify-center space-x-2 px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 transition-colors"
      >
        <Navigation className="h-4 w-4" />
        <span>Use Current Location</span>
      </button>
    </div>
  );
}
