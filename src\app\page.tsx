'use client';

import { useState, useEffect } from 'react';
import { useSession, signIn, signOut } from 'next-auth/react';
import MapComponent from '@/components/MapComponent';
import LocationSearch from '@/components/LocationSearch';
import LocationInfo from '@/components/LocationInfo';
import SavedLocations from '@/components/SavedLocations';
import { MapPin, User, LogOut, Heart } from 'lucide-react';

export default function Home() {
  const { data: session, status } = useSession();
  const [selectedLocation, setSelectedLocation] = useState<any>(null);
  const [showSavedLocations, setShowSavedLocations] = useState(false);
  const [userLocation, setUserLocation] = useState<{ lat: number; lng: number } | null>(null);

  useEffect(() => {
    // Get user's current location
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setUserLocation({
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          });
        },
        (error) => {
          console.error('Error getting location:', error);
          // Default to New York City if location access is denied
          setUserLocation({ lat: 40.7128, lng: -74.0060 });
        }
      );
    } else {
      // Default to New York City if geolocation is not supported
      setUserLocation({ lat: 40.7128, lng: -74.0060 });
    }
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <MapPin className="h-8 w-8 text-blue-600" />
              <h1 className="text-2xl font-bold text-gray-900">WikiCity</h1>
            </div>

            <div className="flex items-center space-x-4">
              {session ? (
                <>
                  <button
                    onClick={() => setShowSavedLocations(!showSavedLocations)}
                    className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                  >
                    <Heart className="h-4 w-4" />
                    <span>Saved</span>
                  </button>
                  <div className="flex items-center space-x-2">
                    <User className="h-5 w-5 text-gray-600" />
                    <span className="text-sm text-gray-700">{session.user?.name}</span>
                  </div>
                  <button
                    onClick={() => signOut()}
                    className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 transition-colors"
                  >
                    <LogOut className="h-4 w-4" />
                    <span>Sign Out</span>
                  </button>
                </>
              ) : (
                <button
                  onClick={() => signIn('google')}
                  className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
                >
                  <User className="h-4 w-4" />
                  <span>Sign In with Google</span>
                </button>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-8rem)]">
          {/* Search and Info Panel */}
          <div className="lg:col-span-1 space-y-6">
            <LocationSearch
              onLocationSelect={setSelectedLocation}
              userLocation={userLocation}
            />

            {selectedLocation && (
              <LocationInfo
                location={selectedLocation}
                session={session}
              />
            )}

            {showSavedLocations && session && (
              <SavedLocations
                session={session}
                onLocationSelect={setSelectedLocation}
              />
            )}
          </div>

          {/* Map */}
          <div className="lg:col-span-3">
            <div className="h-full rounded-lg overflow-hidden shadow-lg">
              {userLocation && (
                <MapComponent
                  center={userLocation}
                  onLocationSelect={setSelectedLocation}
                  selectedLocation={selectedLocation}
                />
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
