'use client';

import { useState, useEffect } from 'react';
import { Session } from 'next-auth';
import { Heart, MapPin, Trash2, Star } from 'lucide-react';
import { supabase } from '@/lib/supabase';

interface SavedLocationsProps {
  session: Session;
  onLocationSelect: (location: any) => void;
}

export default function SavedLocations({ session, onLocationSelect }: SavedLocationsProps) {
  const [savedLocations, setSavedLocations] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchSavedLocations();
  }, [session]);

  const fetchSavedLocations = async () => {
    if (!session?.user?.email) return;

    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('saved_locations')
        .select('*')
        .eq('user_email', session.user.email)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching saved locations:', error);
      } else {
        setSavedLocations(data || []);
      }
    } catch (error) {
      console.error('Error fetching saved locations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveLocation = async (locationId: string) => {
    try {
      const { error } = await supabase
        .from('saved_locations')
        .delete()
        .eq('id', locationId);

      if (!error) {
        setSavedLocations(prev => prev.filter(loc => loc.id !== locationId));
      }
    } catch (error) {
      console.error('Error removing saved location:', error);
    }
  };

  const handleLocationClick = (location: any) => {
    onLocationSelect({
      place_id: location.place_id,
      name: location.name,
      location: location.location,
      formatted_address: location.formatted_address,
      types: location.types,
      rating: location.rating,
      vicinity: location.vicinity,
    });
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Heart className="h-5 w-5 mr-2 text-red-600" />
          Saved Locations
        </h3>
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-sm text-gray-600">Loading saved locations...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
        <Heart className="h-5 w-5 mr-2 text-red-600" />
        Saved Locations
      </h3>

      {savedLocations.length === 0 ? (
        <div className="text-center py-8">
          <Heart className="h-12 w-12 text-gray-300 mx-auto mb-3" />
          <p className="text-sm text-gray-500">No saved locations yet</p>
          <p className="text-xs text-gray-400 mt-1">
            Click the heart icon on any location to save it
          </p>
        </div>
      ) : (
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {savedLocations.map((location) => (
            <div
              key={location.id}
              className="border border-gray-200 rounded-lg p-3 hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-start justify-between">
                <button
                  onClick={() => handleLocationClick(location)}
                  className="flex-1 text-left"
                >
                  <h4 className="font-medium text-gray-900 text-sm mb-1">
                    {location.name}
                  </h4>
                  
                  {location.formatted_address && (
                    <div className="flex items-center text-xs text-gray-600 mb-2">
                      <MapPin className="h-3 w-3 mr-1 flex-shrink-0" />
                      <span className="truncate">{location.formatted_address}</span>
                    </div>
                  )}

                  {location.rating && (
                    <div className="flex items-center mb-2">
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-3 w-3 ${
                              i < Math.floor(location.rating)
                                ? 'text-yellow-400 fill-current'
                                : 'text-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                      <span className="ml-1 text-xs text-gray-600">
                        {location.rating.toFixed(1)}
                      </span>
                    </div>
                  )}

                  {location.types && location.types.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {location.types.slice(0, 2).map((type: string) => (
                        <span
                          key={type}
                          className="px-2 py-0.5 text-xs font-medium text-blue-600 bg-blue-50 rounded-full"
                        >
                          {type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </span>
                      ))}
                    </div>
                  )}
                </button>

                <button
                  onClick={() => handleRemoveLocation(location.id)}
                  className="ml-2 p-1 text-gray-400 hover:text-red-600 transition-colors"
                  title="Remove from saved locations"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
