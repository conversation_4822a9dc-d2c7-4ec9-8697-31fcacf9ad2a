'use client';

import { useEffect, useRef, useState } from 'react';
import { Loader } from '@googlemaps/js-api-loader';

interface MapComponentProps {
  center: { lat: number; lng: number };
  onLocationSelect: (location: any) => void;
  selectedLocation?: any;
}

export default function MapComponent({ center, onLocationSelect, selectedLocation }: MapComponentProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [markers, setMarkers] = useState<google.maps.Marker[]>([]);
  const [placesService, setPlacesService] = useState<google.maps.places.PlacesService | null>(null);

  useEffect(() => {
    const initMap = async () => {
      const loader = new Loader({
        apiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || '',
        version: 'weekly',
        libraries: ['places'],
      });

      try {
        await loader.load();
        
        if (mapRef.current) {
          const mapInstance = new google.maps.Map(mapRef.current, {
            center,
            zoom: 14,
            styles: [
              {
                featureType: 'poi',
                elementType: 'labels',
                stylers: [{ visibility: 'on' }],
              },
            ],
          });

          setMap(mapInstance);
          setPlacesService(new google.maps.places.PlacesService(mapInstance));

          // Add user location marker
          new google.maps.Marker({
            position: center,
            map: mapInstance,
            title: 'Your Location',
            icon: {
              url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="12" cy="12" r="8" fill="#3B82F6" stroke="#FFFFFF" stroke-width="2"/>
                  <circle cx="12" cy="12" r="3" fill="#FFFFFF"/>
                </svg>
              `),
              scaledSize: new google.maps.Size(24, 24),
            },
          });

          // Search for nearby points of interest
          searchNearbyPlaces(mapInstance, center);
        }
      } catch (error) {
        console.error('Error loading Google Maps:', error);
      }
    };

    initMap();
  }, [center]);

  const searchNearbyPlaces = (mapInstance: google.maps.Map, location: { lat: number; lng: number }) => {
    if (!placesService) return;

    const request = {
      location: new google.maps.LatLng(location.lat, location.lng),
      radius: 2000,
      type: 'point_of_interest' as google.maps.places.PlaceType,
    };

    placesService.nearbySearch(request, (results, status) => {
      if (status === google.maps.places.PlacesServiceStatus.OK && results) {
        // Clear existing markers
        markers.forEach(marker => marker.setMap(null));
        const newMarkers: google.maps.Marker[] = [];

        results.slice(0, 20).forEach((place) => {
          if (place.geometry?.location) {
            const marker = new google.maps.Marker({
              position: place.geometry.location,
              map: mapInstance,
              title: place.name,
              icon: {
                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" fill="#EF4444" stroke="#FFFFFF" stroke-width="2"/>
                    <circle cx="12" cy="10" r="3" fill="#FFFFFF"/>
                  </svg>
                `),
                scaledSize: new google.maps.Size(20, 20),
              },
            });

            marker.addListener('click', () => {
              onLocationSelect({
                place_id: place.place_id,
                name: place.name,
                location: {
                  lat: place.geometry!.location!.lat(),
                  lng: place.geometry!.location!.lng(),
                },
                types: place.types,
                rating: place.rating,
                vicinity: place.vicinity,
              });
            });

            newMarkers.push(marker);
          }
        });

        setMarkers(newMarkers);
      }
    });
  };

  // Update map center when center prop changes
  useEffect(() => {
    if (map) {
      map.setCenter(center);
      searchNearbyPlaces(map, center);
    }
  }, [center, map, placesService]);

  // Highlight selected location
  useEffect(() => {
    if (selectedLocation && map) {
      map.setCenter(selectedLocation.location);
      map.setZoom(16);
    }
  }, [selectedLocation, map]);

  return (
    <div className="w-full h-full">
      <div ref={mapRef} className="w-full h-full" />
    </div>
  );
}
