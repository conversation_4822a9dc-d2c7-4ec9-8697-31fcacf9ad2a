import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder_key';

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database types
export interface SavedLocation {
  id: string;
  user_email: string;
  place_id: string;
  name: string;
  location: {
    lat: number;
    lng: number;
  };
  formatted_address?: string;
  types?: string[];
  rating?: number;
  vicinity?: string;
  created_at: string;
  updated_at: string;
}
