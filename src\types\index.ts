export interface Location {
  lat: number;
  lng: number;
}

export interface PlaceDetails {
  place_id?: string;
  name: string;
  location: Location;
  formatted_address?: string;
  types?: string[];
  rating?: number;
  vicinity?: string;
}

export interface SavedLocation extends PlaceDetails {
  id: string;
  user_email: string;
  created_at: string;
  updated_at: string;
}

export interface WikipediaInfo {
  title: string;
  extract: string;
  content_urls?: {
    desktop?: {
      page: string;
    };
  };
}
