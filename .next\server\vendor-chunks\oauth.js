/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/oauth";
exports.ids = ["vendor-chunks/oauth"];
exports.modules = {

/***/ "(rsc)/./node_modules/oauth/index.js":
/*!*************************************!*\
  !*** ./node_modules/oauth/index.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("exports.OAuth = __webpack_require__(/*! ./lib/oauth */ \"(rsc)/./node_modules/oauth/lib/oauth.js\").OAuth;\nexports.OAuthEcho = __webpack_require__(/*! ./lib/oauth */ \"(rsc)/./node_modules/oauth/lib/oauth.js\").OAuthEcho;\nexports.OAuth2 = __webpack_require__(/*! ./lib/oauth2 */ \"(rsc)/./node_modules/oauth/lib/oauth2.js\").OAuth2;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb2F1dGgvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUEsdUdBQTRDO0FBQzVDLCtHQUFvRDtBQUNwRCwyR0FBK0MiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXRlbnRlXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXHdpa2ktY2l0eVxcbm9kZV9tb2R1bGVzXFxvYXV0aFxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0cy5PQXV0aCA9IHJlcXVpcmUoXCIuL2xpYi9vYXV0aFwiKS5PQXV0aDtcbmV4cG9ydHMuT0F1dGhFY2hvID0gcmVxdWlyZShcIi4vbGliL29hdXRoXCIpLk9BdXRoRWNobztcbmV4cG9ydHMuT0F1dGgyID0gcmVxdWlyZShcIi4vbGliL29hdXRoMlwiKS5PQXV0aDI7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/oauth/lib/_utils.js":
/*!******************************************!*\
  !*** ./node_modules/oauth/lib/_utils.js ***!
  \******************************************/
/***/ ((module) => {

eval("// Returns true if this is a host that closes *before* it ends?!?!\nmodule.exports.isAnEarlyCloseHost= function( hostName ) {\n  return hostName && hostName.match(\".*google(apis)?.com$\")\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb2F1dGgvbGliL191dGlscy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFV0ZW50ZVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFx3aWtpLWNpdHlcXG5vZGVfbW9kdWxlc1xcb2F1dGhcXGxpYlxcX3V0aWxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFJldHVybnMgdHJ1ZSBpZiB0aGlzIGlzIGEgaG9zdCB0aGF0IGNsb3NlcyAqYmVmb3JlKiBpdCBlbmRzPyE/IVxubW9kdWxlLmV4cG9ydHMuaXNBbkVhcmx5Q2xvc2VIb3N0PSBmdW5jdGlvbiggaG9zdE5hbWUgKSB7XG4gIHJldHVybiBob3N0TmFtZSAmJiBob3N0TmFtZS5tYXRjaChcIi4qZ29vZ2xlKGFwaXMpPy5jb20kXCIpXG59Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/lib/_utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/oauth/lib/oauth.js":
/*!*****************************************!*\
  !*** ./node_modules/oauth/lib/oauth.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var crypto= __webpack_require__(/*! crypto */ \"crypto\"),\n    sha1= __webpack_require__(/*! ./sha1 */ \"(rsc)/./node_modules/oauth/lib/sha1.js\"),\n    http= __webpack_require__(/*! http */ \"http\"),\n    https= __webpack_require__(/*! https */ \"https\"),\n    URL= __webpack_require__(/*! url */ \"url\"),\n    querystring= __webpack_require__(/*! querystring */ \"querystring\"),\n    OAuthUtils= __webpack_require__(/*! ./_utils */ \"(rsc)/./node_modules/oauth/lib/_utils.js\");\n\nexports.OAuth= function(requestUrl, accessUrl, consumerKey, consumerSecret, version, authorize_callback, signatureMethod, nonceSize, customHeaders) {\n  this._isEcho = false;\n\n  this._requestUrl= requestUrl;\n  this._accessUrl= accessUrl;\n  this._consumerKey= consumerKey;\n  this._consumerSecret= this._encodeData( consumerSecret );\n  if (signatureMethod == \"RSA-SHA1\") {\n    this._privateKey = consumerSecret;\n  }\n  this._version= version;\n  if( authorize_callback === undefined ) {\n    this._authorize_callback= \"oob\";\n  }\n  else {\n    this._authorize_callback= authorize_callback;\n  }\n\n  if( signatureMethod != \"PLAINTEXT\" && signatureMethod != \"HMAC-SHA1\" && signatureMethod != \"RSA-SHA1\")\n    throw new Error(\"Un-supported signature method: \" + signatureMethod )\n  this._signatureMethod= signatureMethod;\n  this._nonceSize= nonceSize || 32;\n  this._headers= customHeaders || {\"Accept\" : \"*/*\",\n                                   \"Connection\" : \"close\",\n                                   \"User-Agent\" : \"Node authentication\"}\n  this._clientOptions= this._defaultClientOptions= {\"requestTokenHttpMethod\": \"POST\",\n                                                    \"accessTokenHttpMethod\": \"POST\",\n                                                    \"followRedirects\": true};\n  this._oauthParameterSeperator = \",\";\n};\n\nexports.OAuthEcho= function(realm, verify_credentials, consumerKey, consumerSecret, version, signatureMethod, nonceSize, customHeaders) {\n  this._isEcho = true;\n\n  this._realm= realm;\n  this._verifyCredentials = verify_credentials;\n  this._consumerKey= consumerKey;\n  this._consumerSecret= this._encodeData( consumerSecret );\n  if (signatureMethod == \"RSA-SHA1\") {\n    this._privateKey = consumerSecret;\n  }\n  this._version= version;\n\n  if( signatureMethod != \"PLAINTEXT\" && signatureMethod != \"HMAC-SHA1\" && signatureMethod != \"RSA-SHA1\")\n    throw new Error(\"Un-supported signature method: \" + signatureMethod );\n  this._signatureMethod= signatureMethod;\n  this._nonceSize= nonceSize || 32;\n  this._headers= customHeaders || {\"Accept\" : \"*/*\",\n                                   \"Connection\" : \"close\",\n                                   \"User-Agent\" : \"Node authentication\"};\n  this._oauthParameterSeperator = \",\";\n}\n\nexports.OAuthEcho.prototype = exports.OAuth.prototype;\n\nexports.OAuth.prototype._getTimestamp= function() {\n  return Math.floor( (new Date()).getTime() / 1000 );\n}\n\nexports.OAuth.prototype._encodeData= function(toEncode){\n if( toEncode == null || toEncode == \"\" ) return \"\"\n else {\n    var result= encodeURIComponent(toEncode);\n    // Fix the mismatch between OAuth's  RFC3986's and Javascript's beliefs in what is right and wrong ;)\n    return result.replace(/\\!/g, \"%21\")\n                 .replace(/\\'/g, \"%27\")\n                 .replace(/\\(/g, \"%28\")\n                 .replace(/\\)/g, \"%29\")\n                 .replace(/\\*/g, \"%2A\");\n }\n}\n\nexports.OAuth.prototype._decodeData= function(toDecode) {\n  if( toDecode != null ) {\n    toDecode = toDecode.replace(/\\+/g, \" \");\n  }\n  return decodeURIComponent( toDecode);\n}\n\nexports.OAuth.prototype._getSignature= function(method, url, parameters, tokenSecret) {\n  var signatureBase= this._createSignatureBase(method, url, parameters);\n  return this._createSignature( signatureBase, tokenSecret );\n}\n\nexports.OAuth.prototype._normalizeUrl= function(url) {\n  var parsedUrl= URL.parse(url, true)\n   var port =\"\";\n   if( parsedUrl.port ) {\n     if( (parsedUrl.protocol == \"http:\" && parsedUrl.port != \"80\" ) ||\n         (parsedUrl.protocol == \"https:\" && parsedUrl.port != \"443\") ) {\n           port= \":\" + parsedUrl.port;\n         }\n   }\n\n  if( !parsedUrl.pathname  || parsedUrl.pathname == \"\" ) parsedUrl.pathname =\"/\";\n\n  return parsedUrl.protocol + \"//\" + parsedUrl.hostname + port + parsedUrl.pathname;\n}\n\n// Is the parameter considered an OAuth parameter\nexports.OAuth.prototype._isParameterNameAnOAuthParameter= function(parameter) {\n  var m = parameter.match('^oauth_');\n  if( m && ( m[0] === \"oauth_\" ) ) {\n    return true;\n  }\n  else {\n    return false;\n  }\n};\n\n// build the OAuth request authorization header\nexports.OAuth.prototype._buildAuthorizationHeaders= function(orderedParameters) {\n  var authHeader=\"OAuth \";\n  if( this._isEcho ) {\n    authHeader += 'realm=\"' + this._realm + '\",';\n  }\n\n  for( var i= 0 ; i < orderedParameters.length; i++) {\n     // Whilst the all the parameters should be included within the signature, only the oauth_ arguments\n     // should appear within the authorization header.\n     if( this._isParameterNameAnOAuthParameter(orderedParameters[i][0]) ) {\n      authHeader+= \"\" + this._encodeData(orderedParameters[i][0])+\"=\\\"\"+ this._encodeData(orderedParameters[i][1])+\"\\\"\"+ this._oauthParameterSeperator;\n     }\n  }\n\n  authHeader= authHeader.substring(0, authHeader.length-this._oauthParameterSeperator.length);\n  return authHeader;\n}\n\n// Takes an object literal that represents the arguments, and returns an array\n// of argument/value pairs.\nexports.OAuth.prototype._makeArrayOfArgumentsHash= function(argumentsHash) {\n  var argument_pairs= [];\n  for(var key in argumentsHash ) {\n    if (argumentsHash.hasOwnProperty(key)) {\n       var value= argumentsHash[key];\n       if( Array.isArray(value) ) {\n         for(var i=0;i<value.length;i++) {\n           argument_pairs[argument_pairs.length]= [key, value[i]];\n         }\n       }\n       else {\n         argument_pairs[argument_pairs.length]= [key, value];\n       }\n    }\n  }\n  return argument_pairs;\n}\n\n// Sorts the encoded key value pairs by encoded name, then encoded value\nexports.OAuth.prototype._sortRequestParams= function(argument_pairs) {\n  // Sort by name, then value.\n  argument_pairs.sort(function(a,b) {\n      if ( a[0]== b[0] )  {\n        return a[1] < b[1] ? -1 : 1;\n      }\n      else return a[0] < b[0] ? -1 : 1;\n  });\n\n  return argument_pairs;\n}\n\nexports.OAuth.prototype._normaliseRequestParams= function(args) {\n  var argument_pairs= this._makeArrayOfArgumentsHash(args);\n  // First encode them #3.4.1.3.2 .1\n  for(var i=0;i<argument_pairs.length;i++) {\n    argument_pairs[i][0]= this._encodeData( argument_pairs[i][0] );\n    argument_pairs[i][1]= this._encodeData( argument_pairs[i][1] );\n  }\n\n  // Then sort them #3.4.1.3.2 .2\n  argument_pairs= this._sortRequestParams( argument_pairs );\n\n  // Then concatenate together #3.4.1.3.2 .3 & .4\n  var args= \"\";\n  for(var i=0;i<argument_pairs.length;i++) {\n      args+= argument_pairs[i][0];\n      args+= \"=\"\n      args+= argument_pairs[i][1];\n      if( i < argument_pairs.length-1 ) args+= \"&\";\n  }\n  return args;\n}\n\nexports.OAuth.prototype._createSignatureBase= function(method, url, parameters) {\n  url= this._encodeData( this._normalizeUrl(url) );\n  parameters= this._encodeData( parameters );\n  return method.toUpperCase() + \"&\" + url + \"&\" + parameters;\n}\n\nexports.OAuth.prototype._createSignature= function(signatureBase, tokenSecret) {\n   if( tokenSecret === undefined ) var tokenSecret= \"\";\n   else tokenSecret= this._encodeData( tokenSecret );\n   // consumerSecret is already encoded\n   var key= this._consumerSecret + \"&\" + tokenSecret;\n\n   var hash= \"\"\n   if( this._signatureMethod == \"PLAINTEXT\" ) {\n     hash= key;\n   }\n   else if (this._signatureMethod == \"RSA-SHA1\") {\n     key = this._privateKey || \"\";\n     hash= crypto.createSign(\"RSA-SHA1\").update(signatureBase).sign(key, 'base64');\n   }\n   else {\n       if( crypto.Hmac ) {\n         hash = crypto.createHmac(\"sha1\", key).update(signatureBase).digest(\"base64\");\n       }\n       else {\n         hash= sha1.HMACSHA1(key, signatureBase);\n       }\n   }\n   return hash;\n}\nexports.OAuth.prototype.NONCE_CHARS= ['a','b','c','d','e','f','g','h','i','j','k','l','m','n',\n              'o','p','q','r','s','t','u','v','w','x','y','z','A','B',\n              'C','D','E','F','G','H','I','J','K','L','M','N','O','P',\n              'Q','R','S','T','U','V','W','X','Y','Z','0','1','2','3',\n              '4','5','6','7','8','9'];\n\nexports.OAuth.prototype._getNonce= function(nonceSize) {\n   var result = [];\n   var chars= this.NONCE_CHARS;\n   var char_pos;\n   var nonce_chars_length= chars.length;\n\n   for (var i = 0; i < nonceSize; i++) {\n       char_pos= Math.floor(Math.random() * nonce_chars_length);\n       result[i]=  chars[char_pos];\n   }\n   return result.join('');\n}\n\nexports.OAuth.prototype._createClient= function( port, hostname, method, path, headers, sslEnabled ) {\n  var options = {\n    host: hostname,\n    port: port,\n    path: path,\n    method: method,\n    headers: headers\n  };\n  var httpModel;\n  if( sslEnabled ) {\n    httpModel= https;\n  } else {\n    httpModel= http;\n  }\n  return httpModel.request(options);\n}\n\nexports.OAuth.prototype._prepareParameters= function( oauth_token, oauth_token_secret, method, url, extra_params ) {\n  var oauthParameters= {\n      \"oauth_timestamp\":        this._getTimestamp(),\n      \"oauth_nonce\":            this._getNonce(this._nonceSize),\n      \"oauth_version\":          this._version,\n      \"oauth_signature_method\": this._signatureMethod,\n      \"oauth_consumer_key\":     this._consumerKey\n  };\n\n  if( oauth_token ) {\n    oauthParameters[\"oauth_token\"]= oauth_token;\n  }\n\n  var sig;\n  if( this._isEcho ) {\n    sig = this._getSignature( \"GET\",  this._verifyCredentials,  this._normaliseRequestParams(oauthParameters), oauth_token_secret);\n  }\n  else {\n    if( extra_params ) {\n      for( var key in extra_params ) {\n        if (extra_params.hasOwnProperty(key)) oauthParameters[key]= extra_params[key];\n      }\n    }\n    var parsedUrl= URL.parse( url, false );\n\n    if( parsedUrl.query ) {\n      var key2;\n      var extraParameters= querystring.parse(parsedUrl.query);\n      for(var key in extraParameters ) {\n        var value= extraParameters[key];\n          if( typeof value == \"object\" ){\n            // TODO: This probably should be recursive\n            for(key2 in value){\n              oauthParameters[key + \"[\" + key2 + \"]\"] = value[key2];\n            }\n          } else {\n            oauthParameters[key]= value;\n          }\n        }\n    }\n\n    sig = this._getSignature( method,  url,  this._normaliseRequestParams(oauthParameters), oauth_token_secret);\n  }\n\n  var orderedParameters= this._sortRequestParams( this._makeArrayOfArgumentsHash(oauthParameters) );\n  orderedParameters[orderedParameters.length]= [\"oauth_signature\", sig];\n  return orderedParameters;\n}\n\nexports.OAuth.prototype._performSecureRequest= function( oauth_token, oauth_token_secret, method, url, extra_params, post_body, post_content_type,  callback ) {\n  var orderedParameters= this._prepareParameters(oauth_token, oauth_token_secret, method, url, extra_params);\n\n  if( !post_content_type ) {\n    post_content_type= \"application/x-www-form-urlencoded\";\n  }\n  var parsedUrl= URL.parse( url, false );\n  if( parsedUrl.protocol == \"http:\" && !parsedUrl.port ) parsedUrl.port= 80;\n  if( parsedUrl.protocol == \"https:\" && !parsedUrl.port ) parsedUrl.port= 443;\n\n  var headers= {};\n  var authorization = this._buildAuthorizationHeaders(orderedParameters);\n  if ( this._isEcho ) {\n    headers[\"X-Verify-Credentials-Authorization\"]= authorization;\n  }\n  else {\n    headers[\"Authorization\"]= authorization;\n  }\n\n  headers[\"Host\"] = parsedUrl.host\n\n  for( var key in this._headers ) {\n    if (this._headers.hasOwnProperty(key)) {\n      headers[key]= this._headers[key];\n    }\n  }\n\n  // Filter out any passed extra_params that are really to do with OAuth\n  for(var key in extra_params) {\n    if( this._isParameterNameAnOAuthParameter( key ) ) {\n      delete extra_params[key];\n    }\n  }\n\n  if( (method == \"POST\" || method == \"PUT\")  && ( post_body == null && extra_params != null) ) {\n    // Fix the mismatch between the output of querystring.stringify() and this._encodeData()\n    post_body= querystring.stringify(extra_params)\n                       .replace(/\\!/g, \"%21\")\n                       .replace(/\\'/g, \"%27\")\n                       .replace(/\\(/g, \"%28\")\n                       .replace(/\\)/g, \"%29\")\n                       .replace(/\\*/g, \"%2A\");\n  }\n\n  if( post_body ) {\n      if ( Buffer.isBuffer(post_body) ) {\n          headers[\"Content-length\"]= post_body.length;\n      } else {\n          headers[\"Content-length\"]= Buffer.byteLength(post_body);\n      }\n  } else {\n      headers[\"Content-length\"]= 0;\n  }\n\n  headers[\"Content-Type\"]= post_content_type;\n\n  var path;\n  if( !parsedUrl.pathname  || parsedUrl.pathname == \"\" ) parsedUrl.pathname =\"/\";\n  if( parsedUrl.query ) path= parsedUrl.pathname + \"?\"+ parsedUrl.query ;\n  else path= parsedUrl.pathname;\n\n  var request;\n  if( parsedUrl.protocol == \"https:\" ) {\n    request= this._createClient(parsedUrl.port, parsedUrl.hostname, method, path, headers, true);\n  }\n  else {\n    request= this._createClient(parsedUrl.port, parsedUrl.hostname, method, path, headers);\n  }\n\n  var clientOptions = this._clientOptions;\n  if( callback ) {\n    var data=\"\";\n    var self= this;\n\n    // Some hosts *cough* google appear to close the connection early / send no content-length header\n    // allow this behaviour.\n    var allowEarlyClose= OAuthUtils.isAnEarlyCloseHost( parsedUrl.hostname );\n    var callbackCalled= false;\n    var passBackControl = function( response ) {\n      if(!callbackCalled) {\n        callbackCalled= true;\n        if ( response.statusCode >= 200 && response.statusCode <= 299 ) {\n          callback(null, data, response);\n        } else {\n          // Follow 301 or 302 redirects with Location HTTP header\n          if((response.statusCode == 301 || response.statusCode == 302) && clientOptions.followRedirects && response.headers && response.headers.location) {\n            self._performSecureRequest( oauth_token, oauth_token_secret, method, response.headers.location, extra_params, post_body, post_content_type,  callback);\n          }\n          else {\n            callback({ statusCode: response.statusCode, data: data }, data, response);\n          }\n        }\n      }\n    }\n\n    request.on('response', function (response) {\n      response.setEncoding('utf8');\n      response.on('data', function (chunk) {\n        data+=chunk;\n      });\n      response.on('end', function () {\n        passBackControl( response );\n      });\n      response.on('close', function () {\n        if( allowEarlyClose ) {\n          passBackControl( response );\n        }\n      });\n    });\n\n    request.on(\"error\", function(err) {\n      if(!callbackCalled) {\n        callbackCalled= true;\n        callback( err )\n      }\n    });\n\n    if( (method == \"POST\" || method ==\"PUT\") && post_body != null && post_body != \"\" ) {\n      request.write(post_body);\n    }\n    request.end();\n  }\n  else {\n    if( (method == \"POST\" || method ==\"PUT\") && post_body != null && post_body != \"\" ) {\n      request.write(post_body);\n    }\n    return request;\n  }\n\n  return;\n}\n\nexports.OAuth.prototype.setClientOptions= function(options) {\n  var key,\n      mergedOptions= {},\n      hasOwnProperty= Object.prototype.hasOwnProperty;\n\n  for( key in this._defaultClientOptions ) {\n    if( !hasOwnProperty.call(options, key) ) {\n      mergedOptions[key]= this._defaultClientOptions[key];\n    } else {\n      mergedOptions[key]= options[key];\n    }\n  }\n\n  this._clientOptions= mergedOptions;\n};\n\nexports.OAuth.prototype.getOAuthAccessToken= function(oauth_token, oauth_token_secret, oauth_verifier,  callback) {\n  var extraParams= {};\n  if( typeof oauth_verifier == \"function\" ) {\n    callback= oauth_verifier;\n  } else {\n    extraParams.oauth_verifier= oauth_verifier;\n  }\n\n   this._performSecureRequest( oauth_token, oauth_token_secret, this._clientOptions.accessTokenHttpMethod, this._accessUrl, extraParams, null, null, function(error, data, response) {\n         if( error ) callback(error);\n         else {\n           var results= querystring.parse( data );\n           var oauth_access_token= results[\"oauth_token\"];\n           delete results[\"oauth_token\"];\n           var oauth_access_token_secret= results[\"oauth_token_secret\"];\n           delete results[\"oauth_token_secret\"];\n           callback(null, oauth_access_token, oauth_access_token_secret, results );\n         }\n   })\n}\n\n// Deprecated\nexports.OAuth.prototype.getProtectedResource= function(url, method, oauth_token, oauth_token_secret, callback) {\n  this._performSecureRequest( oauth_token, oauth_token_secret, method, url, null, \"\", null, callback );\n}\n\nexports.OAuth.prototype[\"delete\"]= function(url, oauth_token, oauth_token_secret, callback) {\n  return this._performSecureRequest( oauth_token, oauth_token_secret, \"DELETE\", url, null, \"\", null, callback );\n}\n\nexports.OAuth.prototype.get= function(url, oauth_token, oauth_token_secret, callback) {\n  return this._performSecureRequest( oauth_token, oauth_token_secret, \"GET\", url, null, \"\", null, callback );\n}\n\nexports.OAuth.prototype._putOrPost= function(method, url, oauth_token, oauth_token_secret, post_body, post_content_type, callback) {\n  var extra_params= null;\n  if( typeof post_content_type == \"function\" ) {\n    callback= post_content_type;\n    post_content_type= null;\n  }\n  if ( typeof post_body != \"string\" && !Buffer.isBuffer(post_body) ) {\n    post_content_type= \"application/x-www-form-urlencoded\"\n    extra_params= post_body;\n    post_body= null;\n  }\n  return this._performSecureRequest( oauth_token, oauth_token_secret, method, url, extra_params, post_body, post_content_type, callback );\n}\n\n\nexports.OAuth.prototype.put= function(url, oauth_token, oauth_token_secret, post_body, post_content_type, callback) {\n  return this._putOrPost(\"PUT\", url, oauth_token, oauth_token_secret, post_body, post_content_type, callback);\n}\n\nexports.OAuth.prototype.post= function(url, oauth_token, oauth_token_secret, post_body, post_content_type, callback) {\n  return this._putOrPost(\"POST\", url, oauth_token, oauth_token_secret, post_body, post_content_type, callback);\n}\n\n/**\n * Gets a request token from the OAuth provider and passes that information back\n * to the calling code.\n *\n * The callback should expect a function of the following form:\n *\n * function(err, token, token_secret, parsedQueryString) {}\n *\n * This method has optional parameters so can be called in the following 2 ways:\n *\n * 1) Primary use case: Does a basic request with no extra parameters\n *  getOAuthRequestToken( callbackFunction )\n *\n * 2) As above but allows for provision of extra parameters to be sent as part of the query to the server.\n *  getOAuthRequestToken( extraParams, callbackFunction )\n *\n * N.B. This method will HTTP POST verbs by default, if you wish to override this behaviour you will\n * need to provide a requestTokenHttpMethod option when creating the client.\n *\n **/\nexports.OAuth.prototype.getOAuthRequestToken= function( extraParams, callback ) {\n   if( typeof extraParams == \"function\" ){\n     callback = extraParams;\n     extraParams = {};\n   }\n  // Callbacks are 1.0A related\n  if( this._authorize_callback ) {\n    extraParams[\"oauth_callback\"]= this._authorize_callback;\n  }\n  this._performSecureRequest( null, null, this._clientOptions.requestTokenHttpMethod, this._requestUrl, extraParams, null, null, function(error, data, response) {\n    if( error ) callback(error);\n    else {\n      var results= querystring.parse(data);\n\n      var oauth_token= results[\"oauth_token\"];\n      var oauth_token_secret= results[\"oauth_token_secret\"];\n      delete results[\"oauth_token\"];\n      delete results[\"oauth_token_secret\"];\n      callback(null, oauth_token, oauth_token_secret,  results );\n    }\n  });\n}\n\nexports.OAuth.prototype.signUrl= function(url, oauth_token, oauth_token_secret, method) {\n\n  if( method === undefined ) {\n    var method= \"GET\";\n  }\n\n  var orderedParameters= this._prepareParameters(oauth_token, oauth_token_secret, method, url, {});\n  var parsedUrl= URL.parse( url, false );\n\n  var query=\"\";\n  for( var i= 0 ; i < orderedParameters.length; i++) {\n    query+= orderedParameters[i][0]+\"=\"+ this._encodeData(orderedParameters[i][1]) + \"&\";\n  }\n  query= query.substring(0, query.length-1);\n\n  return parsedUrl.protocol + \"//\"+ parsedUrl.host + parsedUrl.pathname + \"?\" + query;\n};\n\nexports.OAuth.prototype.authHeader= function(url, oauth_token, oauth_token_secret, method) {\n  if( method === undefined ) {\n    var method= \"GET\";\n  }\n\n  var orderedParameters= this._prepareParameters(oauth_token, oauth_token_secret, method, url, {});\n  return this._buildAuthorizationHeaders(orderedParameters);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/lib/oauth.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/oauth/lib/oauth2.js":
/*!******************************************!*\
  !*** ./node_modules/oauth/lib/oauth2.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var querystring= __webpack_require__(/*! querystring */ \"querystring\"),\n    crypto= __webpack_require__(/*! crypto */ \"crypto\"),\n    https= __webpack_require__(/*! https */ \"https\"),\n    http= __webpack_require__(/*! http */ \"http\"),\n    URL= __webpack_require__(/*! url */ \"url\"),\n    OAuthUtils= __webpack_require__(/*! ./_utils */ \"(rsc)/./node_modules/oauth/lib/_utils.js\");\n\nexports.OAuth2= function(clientId, clientSecret, baseSite, authorizePath, accessTokenPath, customHeaders) {\n  this._clientId= clientId;\n  this._clientSecret= clientSecret;\n  this._baseSite= baseSite;\n  this._authorizeUrl= authorizePath || \"/oauth/authorize\";\n  this._accessTokenUrl= accessTokenPath || \"/oauth/access_token\";\n  this._accessTokenName= \"access_token\";\n  this._authMethod= \"Bearer\";\n  this._customHeaders = customHeaders || {};\n  this._useAuthorizationHeaderForGET= false;\n\n  //our agent\n  this._agent = undefined;\n};\n\n// Allows you to set an agent to use instead of the default HTTP or\n// HTTPS agents. Useful when dealing with your own certificates.\nexports.OAuth2.prototype.setAgent = function(agent) {\n  this._agent = agent;\n};\n\n// This 'hack' method is required for sites that don't use\n// 'access_token' as the name of the access token (for requests).\n// ( http://tools.ietf.org/html/draft-ietf-oauth-v2-16#section-7 )\n// it isn't clear what the correct value should be atm, so allowing\n// for specific (temporary?) override for now.\nexports.OAuth2.prototype.setAccessTokenName= function ( name ) {\n  this._accessTokenName= name;\n}\n\n// Sets the authorization method for Authorization header.\n// e.g. Authorization: Bearer <token>  # \"Bearer\" is the authorization method.\nexports.OAuth2.prototype.setAuthMethod = function ( authMethod ) {\n  this._authMethod = authMethod;\n};\n\n\n// If you use the OAuth2 exposed 'get' method (and don't construct your own _request call )\n// this will specify whether to use an 'Authorize' header instead of passing the access_token as a query parameter\nexports.OAuth2.prototype.useAuthorizationHeaderforGET = function(useIt) {\n  this._useAuthorizationHeaderForGET= useIt;\n}\n\nexports.OAuth2.prototype._getAccessTokenUrl= function() {\n  return this._baseSite + this._accessTokenUrl; /* + \"?\" + querystring.stringify(params); */\n}\n\n// Build the authorization header. In particular, build the part after the colon.\n// e.g. Authorization: Bearer <token>  # Build \"Bearer <token>\"\nexports.OAuth2.prototype.buildAuthHeader= function(token) {\n  return this._authMethod + ' ' + token;\n};\n\nexports.OAuth2.prototype._chooseHttpLibrary= function( parsedUrl ) {\n  var http_library= https;\n  // As this is OAUth2, we *assume* https unless told explicitly otherwise.\n  if( parsedUrl.protocol != \"https:\" ) {\n    http_library= http;\n  }\n  return http_library;\n};\n\nexports.OAuth2.prototype._request= function(method, url, headers, post_body, access_token, callback) {\n\n  var parsedUrl= URL.parse( url, true );\n  if( parsedUrl.protocol == \"https:\" && !parsedUrl.port ) {\n    parsedUrl.port= 443;\n  }\n\n  var http_library= this._chooseHttpLibrary( parsedUrl );\n\n\n  var realHeaders= {};\n  for( var key in this._customHeaders ) {\n    realHeaders[key]= this._customHeaders[key];\n  }\n  if( headers ) {\n    for(var key in headers) {\n      realHeaders[key] = headers[key];\n    }\n  }\n  realHeaders['Host']= parsedUrl.host;\n\n  if (!realHeaders['User-Agent']) {\n    realHeaders['User-Agent'] = 'Node-oauth';\n  }\n\n  if( post_body ) {\n      if ( Buffer.isBuffer(post_body) ) {\n          realHeaders[\"Content-Length\"]= post_body.length;\n      } else {\n          realHeaders[\"Content-Length\"]= Buffer.byteLength(post_body);\n      }\n  } else {\n      realHeaders[\"Content-length\"]= 0;\n  }\n\n  if( access_token && !('Authorization' in realHeaders)) {\n    if( ! parsedUrl.query ) parsedUrl.query= {};\n    parsedUrl.query[this._accessTokenName]= access_token;\n  }\n\n  var queryStr= querystring.stringify(parsedUrl.query);\n  if( queryStr ) queryStr=  \"?\" + queryStr;\n  var options = {\n    host:parsedUrl.hostname,\n    port: parsedUrl.port,\n    path: parsedUrl.pathname + queryStr,\n    method: method,\n    headers: realHeaders\n  };\n\n  this._executeRequest( http_library, options, post_body, callback );\n}\n\nexports.OAuth2.prototype._executeRequest= function( http_library, options, post_body, callback ) {\n  // Some hosts *cough* google appear to close the connection early / send no content-length header\n  // allow this behaviour.\n  var allowEarlyClose= OAuthUtils.isAnEarlyCloseHost(options.host);\n  var callbackCalled= false;\n  function passBackControl( response, result ) {\n    if(!callbackCalled) {\n      callbackCalled=true;\n      if( !(response.statusCode >= 200 && response.statusCode <= 299) && (response.statusCode != 301) && (response.statusCode != 302) ) {\n        callback({ statusCode: response.statusCode, data: result });\n      } else {\n        callback(null, result, response);\n      }\n    }\n  }\n\n  var result= \"\";\n\n  //set the agent on the request options\n  if (this._agent) {\n    options.agent = this._agent;\n  }\n\n  var request = http_library.request(options);\n  request.on('response', function (response) {\n    response.on(\"data\", function (chunk) {\n      result+= chunk\n    });\n    response.on(\"close\", function (err) {\n      if( allowEarlyClose ) {\n        passBackControl( response, result );\n      }\n    });\n    response.addListener(\"end\", function () {\n      passBackControl( response, result );\n    });\n  });\n  request.on('error', function(e) {\n    callbackCalled= true;\n    callback(e);\n  });\n\n  if( (options.method == 'POST' || options.method == 'PUT') && post_body ) {\n     request.write(post_body);\n  }\n  request.end();\n}\n\nexports.OAuth2.prototype.getAuthorizeUrl= function( params ) {\n  var params= params || {};\n  params['client_id'] = this._clientId;\n  return this._baseSite + this._authorizeUrl + \"?\" + querystring.stringify(params);\n}\n\nexports.OAuth2.prototype.getOAuthAccessToken= function(code, params, callback) {\n  var params= params || {};\n  params['client_id'] = this._clientId;\n  params['client_secret'] = this._clientSecret;\n  var codeParam = (params.grant_type === 'refresh_token') ? 'refresh_token' : 'code';\n  params[codeParam]= code;\n\n  var post_data= querystring.stringify( params );\n  var post_headers= {\n       'Content-Type': 'application/x-www-form-urlencoded'\n   };\n\n\n  this._request(\"POST\", this._getAccessTokenUrl(), post_headers, post_data, null, function(error, data, response) {\n    if( error )  callback(error);\n    else {\n      var results;\n      try {\n        // As of http://tools.ietf.org/html/draft-ietf-oauth-v2-07\n        // responses should be in JSON\n        results= JSON.parse( data );\n      }\n      catch(e) {\n        // .... However both Facebook + Github currently use rev05 of the spec\n        // and neither seem to specify a content-type correctly in their response headers :(\n        // clients of these services will suffer a *minor* performance cost of the exception\n        // being thrown\n        results= querystring.parse( data );\n      }\n      var access_token= results[\"access_token\"];\n      var refresh_token= results[\"refresh_token\"];\n      delete results[\"refresh_token\"];\n      callback(null, access_token, refresh_token, results); // callback results =-=\n    }\n  });\n}\n\n// Deprecated\nexports.OAuth2.prototype.getProtectedResource= function(url, access_token, callback) {\n  this._request(\"GET\", url, {}, \"\", access_token, callback );\n}\n\nexports.OAuth2.prototype.get= function(url, access_token, callback) {\n  if( this._useAuthorizationHeaderForGET ) {\n    var headers= {'Authorization': this.buildAuthHeader(access_token) }\n    access_token= null;\n  }\n  else {\n    headers= {};\n  }\n  this._request(\"GET\", url, headers, \"\", access_token, callback );\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/lib/oauth2.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/oauth/lib/sha1.js":
/*!****************************************!*\
  !*** ./node_modules/oauth/lib/sha1.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/*\n * A JavaScript implementation of the Secure Hash Algorithm, SHA-1, as defined\n * in FIPS 180-1\n * Version 2.2 Copyright Paul Johnston 2000 - 2009.\n * Other contributors: Greg Holt, Andrew Kepert, Ydnar, Lostinet\n * Distributed under the BSD License\n * See http://pajhome.org.uk/crypt/md5 for details.\n */\n\n/*\n * Configurable variables. You may need to tweak these to be compatible with\n * the server-side, but the defaults work in most cases.\n */\nvar hexcase = 1;  /* hex output format. 0 - lowercase; 1 - uppercase        */\nvar b64pad  = \"=\"; /* base-64 pad character. \"=\" for strict RFC compliance   */\n\n/*\n * These are the functions you'll usually want to call\n * They take string arguments and return either hex or base-64 encoded strings\n */\nfunction hex_sha1(s)    { return rstr2hex(rstr_sha1(str2rstr_utf8(s))); }\nfunction b64_sha1(s)    { return rstr2b64(rstr_sha1(str2rstr_utf8(s))); }\nfunction any_sha1(s, e) { return rstr2any(rstr_sha1(str2rstr_utf8(s)), e); }\nfunction hex_hmac_sha1(k, d)\n  { return rstr2hex(rstr_hmac_sha1(str2rstr_utf8(k), str2rstr_utf8(d))); }\nfunction b64_hmac_sha1(k, d)\n  { return rstr2b64(rstr_hmac_sha1(str2rstr_utf8(k), str2rstr_utf8(d))); }\nfunction any_hmac_sha1(k, d, e)\n  { return rstr2any(rstr_hmac_sha1(str2rstr_utf8(k), str2rstr_utf8(d)), e); }\n\n/*\n * Perform a simple self-test to see if the VM is working\n */\nfunction sha1_vm_test()\n{\n  return hex_sha1(\"abc\").toLowerCase() == \"a9993e364706816aba3e25717850c26c9cd0d89d\";\n}\n\n/*\n * Calculate the SHA1 of a raw string\n */\nfunction rstr_sha1(s)\n{\n  return binb2rstr(binb_sha1(rstr2binb(s), s.length * 8));\n}\n\n/*\n * Calculate the HMAC-SHA1 of a key and some data (raw strings)\n */\nfunction rstr_hmac_sha1(key, data)\n{\n  var bkey = rstr2binb(key);\n  if(bkey.length > 16) bkey = binb_sha1(bkey, key.length * 8);\n\n  var ipad = Array(16), opad = Array(16);\n  for(var i = 0; i < 16; i++)\n  {\n    ipad[i] = bkey[i] ^ 0x36363636;\n    opad[i] = bkey[i] ^ 0x5C5C5C5C;\n  }\n\n  var hash = binb_sha1(ipad.concat(rstr2binb(data)), 512 + data.length * 8);\n  return binb2rstr(binb_sha1(opad.concat(hash), 512 + 160));\n}\n\n/*\n * Convert a raw string to a hex string\n */\nfunction rstr2hex(input)\n{\n  try { hexcase } catch(e) { hexcase=0; }\n  var hex_tab = hexcase ? \"0123456789ABCDEF\" : \"0123456789abcdef\";\n  var output = \"\";\n  var x;\n  for(var i = 0; i < input.length; i++)\n  {\n    x = input.charCodeAt(i);\n    output += hex_tab.charAt((x >>> 4) & 0x0F)\n           +  hex_tab.charAt( x        & 0x0F);\n  }\n  return output;\n}\n\n/*\n * Convert a raw string to a base-64 string\n */\nfunction rstr2b64(input)\n{\n  try { b64pad } catch(e) { b64pad=''; }\n  var tab = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\n  var output = \"\";\n  var len = input.length;\n  for(var i = 0; i < len; i += 3)\n  {\n    var triplet = (input.charCodeAt(i) << 16)\n                | (i + 1 < len ? input.charCodeAt(i+1) << 8 : 0)\n                | (i + 2 < len ? input.charCodeAt(i+2)      : 0);\n    for(var j = 0; j < 4; j++)\n    {\n      if(i * 8 + j * 6 > input.length * 8) output += b64pad;\n      else output += tab.charAt((triplet >>> 6*(3-j)) & 0x3F);\n    }\n  }\n  return output;\n}\n\n/*\n * Convert a raw string to an arbitrary string encoding\n */\nfunction rstr2any(input, encoding)\n{\n  var divisor = encoding.length;\n  var remainders = Array();\n  var i, q, x, quotient;\n\n  /* Convert to an array of 16-bit big-endian values, forming the dividend */\n  var dividend = Array(Math.ceil(input.length / 2));\n  for(i = 0; i < dividend.length; i++)\n  {\n    dividend[i] = (input.charCodeAt(i * 2) << 8) | input.charCodeAt(i * 2 + 1);\n  }\n\n  /*\n   * Repeatedly perform a long division. The binary array forms the dividend,\n   * the length of the encoding is the divisor. Once computed, the quotient\n   * forms the dividend for the next step. We stop when the dividend is zero.\n   * All remainders are stored for later use.\n   */\n  while(dividend.length > 0)\n  {\n    quotient = Array();\n    x = 0;\n    for(i = 0; i < dividend.length; i++)\n    {\n      x = (x << 16) + dividend[i];\n      q = Math.floor(x / divisor);\n      x -= q * divisor;\n      if(quotient.length > 0 || q > 0)\n        quotient[quotient.length] = q;\n    }\n    remainders[remainders.length] = x;\n    dividend = quotient;\n  }\n\n  /* Convert the remainders to the output string */\n  var output = \"\";\n  for(i = remainders.length - 1; i >= 0; i--)\n    output += encoding.charAt(remainders[i]);\n\n  /* Append leading zero equivalents */\n  var full_length = Math.ceil(input.length * 8 /\n                                    (Math.log(encoding.length) / Math.log(2)))\n  for(i = output.length; i < full_length; i++)\n    output = encoding[0] + output;\n\n  return output;\n}\n\n/*\n * Encode a string as utf-8.\n * For efficiency, this assumes the input is valid utf-16.\n */\nfunction str2rstr_utf8(input)\n{\n  var output = \"\";\n  var i = -1;\n  var x, y;\n\n  while(++i < input.length)\n  {\n    /* Decode utf-16 surrogate pairs */\n    x = input.charCodeAt(i);\n    y = i + 1 < input.length ? input.charCodeAt(i + 1) : 0;\n    if(0xD800 <= x && x <= 0xDBFF && 0xDC00 <= y && y <= 0xDFFF)\n    {\n      x = 0x10000 + ((x & 0x03FF) << 10) + (y & 0x03FF);\n      i++;\n    }\n\n    /* Encode output as utf-8 */\n    if(x <= 0x7F)\n      output += String.fromCharCode(x);\n    else if(x <= 0x7FF)\n      output += String.fromCharCode(0xC0 | ((x >>> 6 ) & 0x1F),\n                                    0x80 | ( x         & 0x3F));\n    else if(x <= 0xFFFF)\n      output += String.fromCharCode(0xE0 | ((x >>> 12) & 0x0F),\n                                    0x80 | ((x >>> 6 ) & 0x3F),\n                                    0x80 | ( x         & 0x3F));\n    else if(x <= 0x1FFFFF)\n      output += String.fromCharCode(0xF0 | ((x >>> 18) & 0x07),\n                                    0x80 | ((x >>> 12) & 0x3F),\n                                    0x80 | ((x >>> 6 ) & 0x3F),\n                                    0x80 | ( x         & 0x3F));\n  }\n  return output;\n}\n\n/*\n * Encode a string as utf-16\n */\nfunction str2rstr_utf16le(input)\n{\n  var output = \"\";\n  for(var i = 0; i < input.length; i++)\n    output += String.fromCharCode( input.charCodeAt(i)        & 0xFF,\n                                  (input.charCodeAt(i) >>> 8) & 0xFF);\n  return output;\n}\n\nfunction str2rstr_utf16be(input)\n{\n  var output = \"\";\n  for(var i = 0; i < input.length; i++)\n    output += String.fromCharCode((input.charCodeAt(i) >>> 8) & 0xFF,\n                                   input.charCodeAt(i)        & 0xFF);\n  return output;\n}\n\n/*\n * Convert a raw string to an array of big-endian words\n * Characters >255 have their high-byte silently ignored.\n */\nfunction rstr2binb(input)\n{\n  var output = Array(input.length >> 2);\n  for(var i = 0; i < output.length; i++)\n    output[i] = 0;\n  for(var i = 0; i < input.length * 8; i += 8)\n    output[i>>5] |= (input.charCodeAt(i / 8) & 0xFF) << (24 - i % 32);\n  return output;\n}\n\n/*\n * Convert an array of big-endian words to a string\n */\nfunction binb2rstr(input)\n{\n  var output = \"\";\n  for(var i = 0; i < input.length * 32; i += 8)\n    output += String.fromCharCode((input[i>>5] >>> (24 - i % 32)) & 0xFF);\n  return output;\n}\n\n/*\n * Calculate the SHA-1 of an array of big-endian words, and a bit length\n */\nfunction binb_sha1(x, len)\n{\n  /* append padding */\n  x[len >> 5] |= 0x80 << (24 - len % 32);\n  x[((len + 64 >> 9) << 4) + 15] = len;\n\n  var w = Array(80);\n  var a =  1732584193;\n  var b = -271733879;\n  var c = -1732584194;\n  var d =  271733878;\n  var e = -1009589776;\n\n  for(var i = 0; i < x.length; i += 16)\n  {\n    var olda = a;\n    var oldb = b;\n    var oldc = c;\n    var oldd = d;\n    var olde = e;\n\n    for(var j = 0; j < 80; j++)\n    {\n      if(j < 16) w[j] = x[i + j];\n      else w[j] = bit_rol(w[j-3] ^ w[j-8] ^ w[j-14] ^ w[j-16], 1);\n      var t = safe_add(safe_add(bit_rol(a, 5), sha1_ft(j, b, c, d)),\n                       safe_add(safe_add(e, w[j]), sha1_kt(j)));\n      e = d;\n      d = c;\n      c = bit_rol(b, 30);\n      b = a;\n      a = t;\n    }\n\n    a = safe_add(a, olda);\n    b = safe_add(b, oldb);\n    c = safe_add(c, oldc);\n    d = safe_add(d, oldd);\n    e = safe_add(e, olde);\n  }\n  return Array(a, b, c, d, e);\n\n}\n\n/*\n * Perform the appropriate triplet combination function for the current\n * iteration\n */\nfunction sha1_ft(t, b, c, d)\n{\n  if(t < 20) return (b & c) | ((~b) & d);\n  if(t < 40) return b ^ c ^ d;\n  if(t < 60) return (b & c) | (b & d) | (c & d);\n  return b ^ c ^ d;\n}\n\n/*\n * Determine the appropriate additive constant for the current iteration\n */\nfunction sha1_kt(t)\n{\n  return (t < 20) ?  1518500249 : (t < 40) ?  1859775393 :\n         (t < 60) ? -1894007588 : -899497514;\n}\n\n/*\n * Add integers, wrapping at 2^32. This uses 16-bit operations internally\n * to work around bugs in some JS interpreters.\n */\nfunction safe_add(x, y)\n{\n  var lsw = (x & 0xFFFF) + (y & 0xFFFF);\n  var msw = (x >> 16) + (y >> 16) + (lsw >> 16);\n  return (msw << 16) | (lsw & 0xFFFF);\n}\n\n/*\n * Bitwise rotate a 32-bit number to the left.\n */\nfunction bit_rol(num, cnt)\n{\n  return (num << cnt) | (num >>> (32 - cnt));\n}\n\nexports.HMACSHA1= function(key, data) {\n  return b64_hmac_sha1(key, data);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/lib/sha1.js\n");

/***/ })

};
;