# WikiCity - Location Discovery App

A comprehensive web application built with Next.js 14, TypeScript, and Tailwind CSS that helps users discover points of interest when visiting new locations. The app integrates Google Maps, Wikipedia, and user authentication to provide a rich location discovery experience.

## Features

- **Interactive Map Interface**: Google Maps integration with clickable markers for points of interest
- **Location Search**: Search for places using Google Places API with autocomplete
- **Wikipedia Integration**: Automatic fetching of relevant Wikipedia information for selected locations
- **User Authentication**: Google OAuth authentication for personalized features
- **Save Locations**: Authenticated users can save interesting locations for future reference
- **Responsive Design**: Fully responsive design that works on desktop and mobile devices
- **Real-time Updates**: Live search results and map updates

## Tech Stack

- **Frontend**: Next.js 14 with App Router, TypeScript, Tailwind CSS
- **Maps**: Google Maps JavaScript API, Google Places API
- **Authentication**: NextAuth.js with Google OAuth
- **Database**: Supabase (PostgreSQL)
- **Icons**: Lucide React
- **Styling**: Tailwind CSS

## Prerequisites

Before running this application, you'll need to obtain API keys and set up accounts for:

1. **Google Cloud Platform** (for Maps and OAuth)
2. **Supabase** (for database)

## Setup Instructions

### 1. Clone the Repository

```bash
git clone <repository-url>
cd wiki-city
npm install
```

### 2. Environment Variables

Copy the example environment file and fill in your API keys:

```bash
cp .env.example .env.local
```

Edit `.env.local` with your actual API keys:

```env
# Google Maps API Key
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# Google OAuth Credentials
GOOGLE_CLIENT_ID=your_google_oauth_client_id_here
GOOGLE_CLIENT_SECRET=your_google_oauth_client_secret_here

# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret_here

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
```

### 3. Google Cloud Platform Setup

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the following APIs:
   - Maps JavaScript API
   - Places API
   - Geocoding API
4. Create credentials:
   - **API Key** for Maps (restrict to your domain)
   - **OAuth 2.0 Client ID** for authentication

### 4. Supabase Setup

1. Create a new project at [Supabase](https://supabase.com/)
2. Go to the SQL Editor and run the following SQL to create the required table:

```sql
-- Create saved_locations table
CREATE TABLE saved_locations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_email TEXT NOT NULL,
  place_id TEXT NOT NULL,
  name TEXT NOT NULL,
  location JSONB NOT NULL,
  formatted_address TEXT,
  types TEXT[],
  rating DECIMAL,
  vicinity TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX idx_saved_locations_user_email ON saved_locations(user_email);
CREATE INDEX idx_saved_locations_place_id ON saved_locations(place_id);

-- Enable Row Level Security
ALTER TABLE saved_locations ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to only access their own data
CREATE POLICY "Users can only access their own saved locations" ON saved_locations
  FOR ALL USING (auth.jwt() ->> 'email' = user_email);
```

3. Get your project URL and anon key from the project settings

### 5. Run the Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## Usage

1. **Browse Locations**: The map will center on your current location (with permission) or default to New York City
2. **Search Places**: Use the search bar to find specific locations or points of interest
3. **Explore Map**: Click on red markers to see information about places
4. **Sign In**: Use Google authentication to access personalized features
5. **Save Locations**: Click the heart icon to save interesting places
6. **View Saved**: Click the "Saved" button to see your saved locations

## Project Structure

```
src/
├── app/
│   ├── api/auth/[...nextauth]/route.ts  # NextAuth configuration
│   ├── layout.tsx                       # Root layout with providers
│   └── page.tsx                         # Main application page
├── components/
│   ├── LocationInfo.tsx                 # Location details with Wikipedia
│   ├── LocationSearch.tsx               # Search functionality
│   ├── MapComponent.tsx                 # Google Maps integration
│   ├── SavedLocations.tsx               # Saved locations management
│   └── SessionProvider.tsx              # NextAuth session provider
└── lib/
    └── supabase.ts                      # Supabase client configuration
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Deployment

The application can be deployed on Vercel, Netlify, or any platform that supports Next.js applications. Make sure to set up the environment variables in your deployment platform.

For Vercel deployment:
1. Connect your GitHub repository
2. Set environment variables in the Vercel dashboard
3. Deploy automatically on push to main branch
