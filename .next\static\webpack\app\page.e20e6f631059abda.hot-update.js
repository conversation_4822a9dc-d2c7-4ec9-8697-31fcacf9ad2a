"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/MapComponent.tsx":
/*!*****************************************!*\
  !*** ./src/components/MapComponent.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MapComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _googlemaps_js_api_loader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @googlemaps/js-api-loader */ \"(app-pages-browser)/./node_modules/@googlemaps/js-api-loader/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction MapComponent(param) {\n    let { center, onLocationSelect, selectedLocation } = param;\n    _s();\n    const mapRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [map, setMap] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [markers, setMarkers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [placesService, setPlacesService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MapComponent.useEffect\": ()=>{\n            const initMap = {\n                \"MapComponent.useEffect.initMap\": async ()=>{\n                    const loader = new _googlemaps_js_api_loader__WEBPACK_IMPORTED_MODULE_2__.Loader({\n                        apiKey: \"AIzaSyCCjPAakviEk74erNNO0qvDivSNVo130hQ\" || 0,\n                        version: 'weekly',\n                        libraries: [\n                            'places'\n                        ]\n                    });\n                    try {\n                        await loader.load();\n                        if (mapRef.current) {\n                            const mapInstance = new google.maps.Map(mapRef.current, {\n                                center,\n                                zoom: 14,\n                                styles: [\n                                    {\n                                        featureType: 'poi',\n                                        elementType: 'labels',\n                                        stylers: [\n                                            {\n                                                visibility: 'on'\n                                            }\n                                        ]\n                                    }\n                                ]\n                            });\n                            setMap(mapInstance);\n                            setPlacesService(new google.maps.places.PlacesService(mapInstance));\n                            // Add user location marker\n                            new google.maps.Marker({\n                                position: center,\n                                map: mapInstance,\n                                title: 'Your Location',\n                                icon: {\n                                    url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent('\\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\\n                  <circle cx=\"12\" cy=\"12\" r=\"8\" fill=\"#3B82F6\" stroke=\"#FFFFFF\" stroke-width=\"2\"/>\\n                  <circle cx=\"12\" cy=\"12\" r=\"3\" fill=\"#FFFFFF\"/>\\n                </svg>\\n              '),\n                                    scaledSize: new google.maps.Size(24, 24)\n                                }\n                            });\n                            // Search for nearby points of interest\n                            searchNearbyPlaces(mapInstance, center);\n                        }\n                    } catch (error) {\n                        console.error('Error loading Google Maps:', error);\n                    }\n                }\n            }[\"MapComponent.useEffect.initMap\"];\n            initMap();\n        }\n    }[\"MapComponent.useEffect\"], [\n        center\n    ]);\n    const searchNearbyPlaces = (mapInstance, location)=>{\n        if (!placesService) return;\n        const request = {\n            location: new google.maps.LatLng(location.lat, location.lng),\n            radius: 2000,\n            type: 'point_of_interest'\n        };\n        placesService.nearbySearch(request, (results, status)=>{\n            if (status === google.maps.places.PlacesServiceStatus.OK && results) {\n                // Clear existing markers\n                markers.forEach((marker)=>marker.setMap(null));\n                const newMarkers = [];\n                results.slice(0, 20).forEach((place)=>{\n                    var _place_geometry;\n                    if ((_place_geometry = place.geometry) === null || _place_geometry === void 0 ? void 0 : _place_geometry.location) {\n                        const marker = new google.maps.Marker({\n                            position: place.geometry.location,\n                            map: mapInstance,\n                            title: place.name,\n                            icon: {\n                                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent('\\n                  <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\\n                    <path d=\"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\" fill=\"#EF4444\" stroke=\"#FFFFFF\" stroke-width=\"2\"/>\\n                    <circle cx=\"12\" cy=\"10\" r=\"3\" fill=\"#FFFFFF\"/>\\n                  </svg>\\n                '),\n                                scaledSize: new google.maps.Size(20, 20)\n                            }\n                        });\n                        marker.addListener('click', ()=>{\n                            onLocationSelect({\n                                place_id: place.place_id,\n                                name: place.name,\n                                location: {\n                                    lat: place.geometry.location.lat(),\n                                    lng: place.geometry.location.lng()\n                                },\n                                types: place.types,\n                                rating: place.rating,\n                                vicinity: place.vicinity\n                            });\n                        });\n                        newMarkers.push(marker);\n                    }\n                });\n                setMarkers(newMarkers);\n            }\n        });\n    };\n    // Update map center when center prop changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MapComponent.useEffect\": ()=>{\n            if (map) {\n                map.setCenter(center);\n                searchNearbyPlaces(map, center);\n            }\n        }\n    }[\"MapComponent.useEffect\"], [\n        center,\n        map,\n        placesService\n    ]);\n    // Highlight selected location\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MapComponent.useEffect\": ()=>{\n            if (selectedLocation && map) {\n                map.setCenter(selectedLocation.location);\n                map.setZoom(16);\n            }\n        }\n    }[\"MapComponent.useEffect\"], [\n        selectedLocation,\n        map\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: mapRef,\n            className: \"w-full h-full\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\MapComponent.tsx\",\n            lineNumber: 145,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\wiki-city\\\\src\\\\components\\\\MapComponent.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\n_s(MapComponent, \"XzAsleCU1WqIBdnT1hqkyTpkFx8=\");\n_c = MapComponent;\nvar _c;\n$RefreshReg$(_c, \"MapComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MapComponent.tsx\n"));

/***/ })

});